import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function IsTimeFormat(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isTimeFormat',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const timeRegex = /^([01]?\d|2[0-3]):([0-5]\d)$/;

          if (typeof value !== 'string' || !timeRegex.test(value)) {
            return false;
          }

          const objectToValidate = args.object as any;
          const availableFrom = objectToValidate.availableFrom;
          const availableTo = objectToValidate.availableTo;

          if (availableTo && availableFrom) {
            const [fromHours, fromMinutes] = availableFrom
              .split(':')
              .map(Number);
            const [toHours, toMinutes] = availableTo.split(':').map(Number);

            const fromTotalMinutes = fromHours * 60 + fromMinutes;
            const toTotalMinutes = toHours * 60 + toMinutes;

            return fromTotalMinutes < toTotalMinutes;
          }

          return true;
        },
      },
    });
  };
}
