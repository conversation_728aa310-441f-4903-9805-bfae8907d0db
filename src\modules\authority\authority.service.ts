import { Injectable } from '@nestjs/common';
import { CreateAuthorityDto } from './dto/create-authority.dto';
import { UpdateAuthorityDto } from './dto/update-authority.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Authority } from './entities/authority.entity';

@Injectable()
export class AuthorityService {
  constructor(
    @InjectRepository(Authority)
    private readonly authorityRepository: Repository<Authority>,
  ) {}

  async onModuleInit() {
    const authorities = await this.authorityRepository.find();
    if (authorities.length === 0) {
      const defaultAuthorities = [
        'ROLE_ADMIN',
        'ROLE_OWNER',
        'ROLE_MANAGER',
        'ROLE_STAFF',
      ];
      const authorities = defaultAuthorities.map((authority) => ({
        name: authority,
      }));
      await this.authorityRepository.save(authorities);
    }
  }

  async create(createAuthorityDto: CreateAuthorityDto): Promise<Authority> {
    const authority = this.authorityRepository.create(createAuthorityDto);
    return await this.authorityRepository.save(authority);
  }

  async findAll(): Promise<Authority[]> {
    return await this.authorityRepository.find();
  }

  async findOne(name: string): Promise<Authority> {
    return await this.authorityRepository.findOne({ where: { name } });
  }

  async update(
    id: string,
    updateAuthorityDto: UpdateAuthorityDto,
  ): Promise<Authority> {
    await this.authorityRepository.update(id, updateAuthorityDto);
    return await this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    await this.authorityRepository.delete(id);
  }
}
