import { Test, TestingModule } from '@nestjs/testing';
import { CommentController } from './comment.controller';
import { CommentService } from './comment.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { Comment } from './entities/comment.entity';

describe('CommentController', () => {
  let controller: CommentController;
  let service: CommentService;

  const mockComment: Comment = {
    id: '1',
    content: 'This is a test comment',
    entityType: 'product',
    entityId: '123',
    isActive: true,
    isEdited: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    author: null,
    parentComment: null,
    replies: [],
  };

  const mockCommentService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findAllActive: jest.fn(),
    findOne: jest.fn(),
    findByEntity: jest.fn(),
    findByAuthor: jest.fn(),
    findReplies: jest.fn(),
    update: jest.fn(),
    updateByUser: jest.fn(),
    remove: jest.fn(),
    softDelete: jest.fn(),
    deleteByUser: jest.fn(),
    toggleActiveStatus: jest.fn(),
    getCommentStats: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommentController],
      providers: [
        {
          provide: CommentService,
          useValue: mockCommentService,
        },
      ],
    }).compile();

    controller = module.get<CommentController>(CommentController);
    service = module.get<CommentService>(CommentService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a comment', async () => {
      const createCommentDto: CreateCommentDto = {
        content: 'This is a test comment',
        entityType: 'product',
        entityId: '123',
        isActive: true,
      };

      mockCommentService.create.mockResolvedValue(mockComment);

      const result = await controller.create(createCommentDto);

      expect(service.create).toHaveBeenCalledWith(createCommentDto);
      expect(result).toEqual(mockComment);
    });
  });

  describe('findAll', () => {
    it('should return all comments', async () => {
      const comments = [mockComment];
      mockCommentService.findAll.mockResolvedValue(comments);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(comments);
    });

    it('should return active comments when active=true', async () => {
      const comments = [mockComment];
      mockCommentService.findAllActive.mockResolvedValue(comments);

      const result = await controller.findAll(true);

      expect(service.findAllActive).toHaveBeenCalled();
      expect(result).toEqual(comments);
    });

    it('should return comments by entity when entityType and entityId provided', async () => {
      const comments = [mockComment];
      mockCommentService.findByEntity.mockResolvedValue(comments);

      const result = await controller.findAll(undefined, 'product', '123');

      expect(service.findByEntity).toHaveBeenCalledWith('product', '123');
      expect(result).toEqual(comments);
    });

    it('should return comments by author when authorId provided', async () => {
      const comments = [mockComment];
      mockCommentService.findByAuthor.mockResolvedValue(comments);

      const result = await controller.findAll(undefined, undefined, undefined, 'user123');

      expect(service.findByAuthor).toHaveBeenCalledWith('user123');
      expect(result).toEqual(comments);
    });
  });

  describe('findOne', () => {
    it('should return a comment by id', async () => {
      mockCommentService.findOne.mockResolvedValue(mockComment);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockComment);
    });
  });

  describe('findReplies', () => {
    it('should return replies for a comment', async () => {
      const replies = [mockComment];
      mockCommentService.findReplies.mockResolvedValue(replies);

      const result = await controller.findReplies('1');

      expect(service.findReplies).toHaveBeenCalledWith('1');
      expect(result).toEqual(replies);
    });
  });

  describe('update', () => {
    it('should update a comment', async () => {
      const updateCommentDto: UpdateCommentDto = {
        content: 'Updated comment',
      };
      const updatedComment = { ...mockComment, content: 'Updated comment' };

      mockCommentService.update.mockResolvedValue(updatedComment);

      const result = await controller.update('1', updateCommentDto);

      expect(service.update).toHaveBeenCalledWith('1', updateCommentDto);
      expect(result).toEqual(updatedComment);
    });
  });

  describe('remove', () => {
    it('should remove a comment', async () => {
      mockCommentService.remove.mockResolvedValue(undefined);

      await controller.remove('1');

      expect(service.remove).toHaveBeenCalledWith('1');
    });
  });

  describe('getStats', () => {
    it('should return comment statistics', async () => {
      const stats = { total: 10, active: 8, inactive: 2 };
      mockCommentService.getCommentStats.mockResolvedValue(stats);

      const result = await controller.getStats('product', '123');

      expect(service.getCommentStats).toHaveBeenCalledWith('product', '123');
      expect(result).toEqual(stats);
    });
  });
});
