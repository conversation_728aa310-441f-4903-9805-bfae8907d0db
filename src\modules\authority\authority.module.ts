import { Module } from '@nestjs/common';
import { AuthorityService } from './authority.service';
import { AuthorityController } from './authority.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Authority } from './entities/authority.entity';

@Module({
  controllers: [AuthorityController],
  providers: [AuthorityService],
  imports: [TypeOrmModule.forFeature([Authority])],
  exports: [AuthorityService],
})
export class AuthorityModule {}
