import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Contact } from './entities/contact.entity';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

@Injectable()
export class ContactService {
  constructor(
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>,
  ) {}

  async create(createContactDto: CreateContactDto): Promise<Contact> {
    // Check if contact with same email already exists
    const existingContact = await this.contactRepository.findOne({
      where: { email: createContactDto.email },
    });

    if (existingContact) {
      throw new ConflictException(
        `Contact with email ${createContactDto.email} already exists`,
      );
    }

    const contact = this.contactRepository.create(createContactDto);
    return await this.contactRepository.save(contact);
  }

  async findAll(): Promise<Contact[]> {
    return await this.contactRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findAllActive(): Promise<Contact[]> {
    return await this.contactRepository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Contact> {
    const contact = await this.contactRepository.findOne({
      where: { id },
    });

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    return contact;
  }

  async findByEmail(email: string): Promise<Contact> {
    const contact = await this.contactRepository.findOne({
      where: { email },
    });

    if (!contact) {
      throw new NotFoundException(`Contact with email ${email} not found`);
    }

    return contact;
  }

  async update(
    id: string,
    updateContactDto: UpdateContactDto,
  ): Promise<Contact> {
    const contact = await this.findOne(id);

    // If email is being updated, check for conflicts
    if (updateContactDto.email && updateContactDto.email !== contact.email) {
      const existingContact = await this.contactRepository.findOne({
        where: { email: updateContactDto.email },
      });

      if (existingContact) {
        throw new ConflictException(
          `Contact with email ${updateContactDto.email} already exists`,
        );
      }
    }

    Object.assign(contact, updateContactDto);
    return await this.contactRepository.save(contact);
  }

  async remove(id: string): Promise<void> {
    const contact = await this.findOne(id);
    await this.contactRepository.remove(contact);
  }

  async toggleActiveStatus(id: string): Promise<Contact> {
    const contact = await this.findOne(id);
    contact.isActive = !contact.isActive;
    return await this.contactRepository.save(contact);
  }

  async searchContacts(searchTerm: string): Promise<Contact[]> {
    return await this.contactRepository
      .createQueryBuilder('contact')
      .where(
        'contact.firstName ILIKE :searchTerm OR contact.lastName ILIKE :searchTerm OR contact.email ILIKE :searchTerm OR contact.company ILIKE :searchTerm',
        { searchTerm: `%${searchTerm}%` },
      )
      .orderBy('contact.createdAt', 'DESC')
      .getMany();
  }
}
