import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactService } from './contact.service';
import { Contact } from './entities/contact.entity';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { NotFoundException, ConflictException } from '@nestjs/common';

describe('ContactService', () => {
  let service: ContactService;
  let repository: Repository<Contact>;

  const mockContact: Contact = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    company: 'Acme Corp',
    position: 'Developer',
    address: '123 Main St',
    city: 'New York',
    country: 'USA',
    notes: 'Test contact',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContactService,
        {
          provide: getRepositoryToken(Contact),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<ContactService>(ContactService);
    repository = module.get<Repository<Contact>>(getRepositoryToken(Contact));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a contact successfully', async () => {
      const createContactDto: CreateContactDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
        company: 'Acme Corp',
        position: 'Developer',
        address: '123 Main St',
        city: 'New York',
        country: 'USA',
        notes: 'Test contact',
        isActive: true,
      };

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.create.mockReturnValue(mockContact);
      mockRepository.save.mockResolvedValue(mockContact);

      const result = await service.create(createContactDto);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { email: createContactDto.email },
      });
      expect(repository.create).toHaveBeenCalledWith(createContactDto);
      expect(repository.save).toHaveBeenCalledWith(mockContact);
      expect(result).toEqual(mockContact);
    });

    it('should throw ConflictException if email already exists', async () => {
      const createContactDto: CreateContactDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      };

      mockRepository.findOne.mockResolvedValue(mockContact);

      await expect(service.create(createContactDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('findAll', () => {
    it('should return all contacts', async () => {
      const contacts = [mockContact];
      mockRepository.find.mockResolvedValue(contacts);

      const result = await service.findAll();

      expect(repository.find).toHaveBeenCalledWith({
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(contacts);
    });
  });

  describe('findOne', () => {
    it('should return a contact by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockContact);

      const result = await service.findOne('1');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual(mockContact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a contact successfully', async () => {
      const updateContactDto: UpdateContactDto = {
        firstName: 'Jane',
      };
      const updatedContact = { ...mockContact, firstName: 'Jane' };

      mockRepository.findOne.mockResolvedValue(mockContact);
      mockRepository.save.mockResolvedValue(updatedContact);

      const result = await service.update('1', updateContactDto);

      expect(result).toEqual(updatedContact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      const updateContactDto: UpdateContactDto = {
        firstName: 'Jane',
      };

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update('1', updateContactDto)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('remove', () => {
    it('should remove a contact successfully', async () => {
      mockRepository.findOne.mockResolvedValue(mockContact);
      mockRepository.remove.mockResolvedValue(mockContact);

      await service.remove('1');

      expect(repository.remove).toHaveBeenCalledWith(mockContact);
    });

    it('should throw NotFoundException if contact not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('toggleActiveStatus', () => {
    it('should toggle contact active status', async () => {
      const inactiveContact = { ...mockContact, isActive: false };
      mockRepository.findOne.mockResolvedValue(mockContact);
      mockRepository.save.mockResolvedValue(inactiveContact);

      const result = await service.toggleActiveStatus('1');

      expect(result.isActive).toBe(false);
    });
  });
});
