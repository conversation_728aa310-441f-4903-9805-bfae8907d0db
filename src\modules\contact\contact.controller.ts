import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ContactService } from './contact.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { Contact } from './entities/contact.entity';

@ApiTags('contacts')
@Controller('contacts')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @Post()
  async create(@Body() createContactDto: CreateContactDto): Promise<Contact> {
    return await this.contactService.create(createContactDto);
  }

  @Get()
  async findAll(
    @Query('active') active?: boolean,
    @Query('search') search?: string,
  ): Promise<Contact[]> {
    if (search) {
      return await this.contactService.searchContacts(search);
    }

    if (active !== undefined) {
      return active
        ? await this.contactService.findAllActive()
        : await this.contactService.findAll();
    }

    return await this.contactService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Contact> {
    return await this.contactService.findOne(id);
  }

  @Get('email/:email')
  async findByEmail(@Param('email') email: string): Promise<Contact> {
    return await this.contactService.findByEmail(email);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
  ): Promise<Contact> {
    return await this.contactService.update(id, updateContactDto);
  }

  @Patch(':id/toggle-status')
  async toggleActiveStatus(@Param('id') id: string): Promise<Contact> {
    return await this.contactService.toggleActiveStatus(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return await this.contactService.remove(id);
  }
}
