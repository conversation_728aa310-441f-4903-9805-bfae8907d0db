import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProviderUserServiceService } from './provider-user-service.service';
import { ProviderUserServiceController } from './provider-user-service.controller';
import { ProviderUserService } from './entities/provider-user-service.entity';
import { User } from '../user/entities/user.entity';
import { Service } from '../service/entities/service.entity';
import { Contact } from '../contact/entities/contact.entity';

@Module({
  controllers: [ProviderUserServiceController],
  providers: [ProviderUserServiceService],
  imports: [TypeOrmModule.forFeature([ProviderUserService, User, Service, Contact])],
  exports: [ProviderUserServiceService],
})
export class ProviderUserServiceModule {}
