import { UserRole } from '../enums/user-role.enum';
import { User } from '../../modules/user/entities/user.entity';

export class RoleUtil {
  static isAdmin(user: User): boolean {
    return user.role === UserRole.ADMIN;
  }

  static isClient(user: User): boolean {
    return user.role === UserRole.CLIENT;
  }

  static isProvider(user: User): boolean {
    return user.role === UserRole.PROVIDER;
  }

  static hasRole(user: User, role: UserRole): boolean {
    return user.role === role;
  }

  static hasAnyRole(user: User, roles: UserRole[]): boolean {
    return roles.includes(user.role);
  }

  static canAccessAdminFeatures(user: User): boolean {
    return user.role === UserRole.ADMIN;
  }

  static canManageServices(user: User): boolean {
    return user.role === UserRole.ADMIN || user.role === UserRole.PROVIDER;
  }

  static canBookServices(user: User): boolean {
    return user.role === UserRole.CLIENT;
  }

  static getRoleDisplayName(role: UserRole): string {
    switch (role) {
      case UserRole.ADMIN:
        return 'Administrator';
      case UserRole.CLIENT:
        return 'Client';
      case UserRole.PROVIDER:
        return 'Service Provider';
      default:
        return 'Unknown';
    }
  }

  static getAllRoles(): UserRole[] {
    return Object.values(UserRole);
  }
}
