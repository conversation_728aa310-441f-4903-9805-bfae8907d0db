-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.1.14
-- http://www.phpmyadmin.net
--
-- Host: 127.0.0.1
-- Generation Time: Apr 24, 2025 at 12:44 PM
-- Server version: 5.6.17
-- PHP Version: 5.5.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- Database: `quickorder_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `addons`
--

CREATE TABLE IF NOT EXISTS `addons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `addon_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_ee37825d7df7f4ddd35927779dd` (`product_id`),
  KEY `FK_815323a8f8b87cf2d3c5e57345e` (`addon_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=8 ;

--
-- Dumping data for table `addons`
--

INSERT INTO `addons` (`id`, `product_id`, `addon_id`) VALUES
(5, 11, 15),
(6, 11, 25),
(7, 11, 24);

-- --------------------------------------------------------

--
-- Table structure for table `audit_log`
--

CREATE TABLE IF NOT EXISTS `audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` enum('CREATE','UPDATE','DELETE','LOGIN','LOGOUT','PURCHASE','ORDER','OTHER') NOT NULL,
  `details` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `performed_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_355ceab7b156f4ac359b19a9380` (`branch_id`),
  KEY `FK_80e2e8cc5eec1cda45594b634c0` (`performed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `authority`
--

CREATE TABLE IF NOT EXISTS `authority` (
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `authority`
--

INSERT INTO `authority` (`name`) VALUES
('ROLE_ADMIN'),
('ROLE_MANAGER'),
('ROLE_OWNER'),
('ROLE_STAFF');

-- --------------------------------------------------------

--
-- Table structure for table `branch`
--

CREATE TABLE IF NOT EXISTS `branch` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `logo_url` varchar(255) DEFAULT 'branch.png',
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_1fc25d651485556af43654bb110` (`store_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=3 ;

--
-- Dumping data for table `branch`
--

INSERT INTO `branch` (`id`, `name`, `address`, `phone`, `email`, `logo_url`, `store_id`) VALUES
(1, 'Espace 360 Tourville', '76410 Tourville-la-Rivière, France', '+33 2 78 08 43 60', '<EMAIL>', 'http://localhost:6061/uploads/branch.png', 1),
(2, 'Espace 360 Luisant', '7 Ter Rue Jean Perrin, 28600 Luisant, France', '+33 2 78 08 43 60', '<EMAIL>', 'http://localhost:6061/uploads/branch.png', 1);

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE IF NOT EXISTS `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `availability_status` tinyint(4) NOT NULL DEFAULT '1',
  `available_from` varchar(255) DEFAULT NULL,
  `available_to` varchar(255) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT 'category.png',
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_16dcaf904b91226a3ba6950b5f0` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=27 ;

--
-- Dumping data for table `category`
--

INSERT INTO `category` (`id`, `name`, `description`, `availability_status`, `available_from`, `available_to`, `image_url`, `branch_id`) VALUES
(11, 'Drinks', 'Drinks', 1, '14:45', '15:45', 'https://vmi1015553.contaboserver.net:6061/uploads/310a7621e8e40d0bbd5138321ad613917.png', 1),
(12, 'Sandwiches', 'Sandwiches', 1, '13:46', '15:46', 'https://vmi1015553.contaboserver.net:6061/uploads/e257bdfc80ee4280c985c78358358570.png', 1),
(13, 'Pizza', 'Pizza', 1, '13:46', '15:46', 'https://vmi1015553.contaboserver.net:6061/uploads/bfd5c105a078af1a32bda87eba1adadce.png', 1),
(14, 'Salads', 'Salads', 1, '14:46', '18:46', 'https://vmi1015553.contaboserver.net:6061/uploads/8dfb46c510ed9ce8f3d2ede719bf59c78.png', 1),
(15, 'Traditional', 'Traditional', 1, '13:47', '18:47', 'https://vmi1015553.contaboserver.net:6061/uploads/d52edbb9353875a6447e7dfffd10545d9.png', 1),
(16, 'Omlettes', 'Omlettes', 1, '13:47', '19:47', 'https://vmi1015553.contaboserver.net:6061/uploads/9831010dee4d8cd09c79ceb108a1fd59c.png', 1),
(17, 'Tacos', 'Tacos', 1, '15:29', '20:29', 'https://vmi1015553.contaboserver.net:6061/uploads/4614b9e7e25b51045d766fb9e2f9bf8ce.png', 1),
(20, 'Burgers', 'Burgers', 1, '13:16', '16:16', 'https://vmi1015553.contaboserver.net:6061/uploads/10f27457d8100942e6c5f348ee6d1f499c.png', 1),
(21, 'Desserts', 'Desserts', 1, '13:17', '17:17', 'https://vmi1015553.contaboserver.net:6061/uploads/5f09110304e592133107a41d5c585e87c8.png', 1),
(22, 'Sushi', 'Sushi', 1, '13:18', '16:18', 'https://vmi1015553.contaboserver.net:6061/uploads/104ad51d6146bbc4138cfd1dd41cdc10ce.jpg', 1),
(23, 'Pasta', 'Pasta', 1, '13:19', '15:19', 'https://vmi1015553.contaboserver.net:6061/uploads/42cd94915cccf2fac3298d79723f54a3.jpg', 1),
(24, 'Vegetarian', 'Vegetarian', 1, '13:20', '17:20', 'https://vmi1015553.contaboserver.net:6061/uploads/ac3b895e86106924c2257f8687ad67aeb.jpg', 1),
(25, 'Breakfast', 'Breakfast', 1, '13:22', '16:22', 'http://localhost:6061/uploads/category.png', 1),
(26, 'Breakfast2', 'Breakfast', 1, '13:22', '16:22', 'http://localhost:6061/uploads/category.png', 2);

-- --------------------------------------------------------

--
-- Table structure for table `coupon`
--

CREATE TABLE IF NOT EXISTS `coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `coupon_type` enum('PERCENTAGE','VALUE') NOT NULL,
  `discount_value` float NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `modified_at` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deleted_at` datetime(6) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_62d3c5b0ce63a82c48e86d904b` (`code`),
  KEY `FK_ef87e85f675967c83dfe80a5139` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `customer`
--

CREATE TABLE IF NOT EXISTS `customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_fdb2f3ad8115da4c7718109a6e` (`email`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=21 ;

--
-- Dumping data for table `customer`
--

INSERT INTO `customer` (`id`, `first_name`, `last_name`, `email`, `address`, `phone`) VALUES
(1, 'Ahmed', 'Bennani', '<EMAIL>', 'Rue Mohammed V, Casablanca', '+212612345678'),
(2, 'Fatima', 'El Mansouri', '<EMAIL>', 'Avenue Hassan II, Rabat', '+212613456789'),
(3, 'Mohammed', 'Bouazza', '<EMAIL>', 'Quartier Souissi, Rabat', '+212614567890'),
(4, 'Khadija', 'Saidi', '<EMAIL>', 'Boulevard Zerktouni, Marrakech', '+212615678901'),
(5, 'Youssef', 'Tazi', '<EMAIL>', 'Route de Safi, Agadir', '+212616789012'),
(6, 'Salma', 'El Idrissi', '<EMAIL>', 'Avenue Moulay Abdellah, Fès', '+212617890123'),
(7, 'Hicham', 'Lamrani', '<EMAIL>', 'Boulevard Mohammed V, Tangier', '+212618901234'),
(8, 'Zineb', 'Boulahcen', '<EMAIL>', 'Rue de la Liberté, Meknès', '+212619012345'),
(9, 'Omar', 'Alaoui', '<EMAIL>', 'Quartier Maârif, Casablanca', '+212620123456'),
(10, 'Leila', 'Fikri', '<EMAIL>', 'Avenue des Fleurs, Oujda', '+212621234567'),
(11, 'Samir', 'Ouaddah', '<EMAIL>', 'Boulevard Anfa, Casablanca', '+212622345678'),
(12, 'Latifa', 'El Fassi', '<EMAIL>', 'Rue des Palmiers, Marrakech', '+212623456789'),
(13, 'Nabil', 'Kabbaj', '<EMAIL>', 'Quartier Hay Ryad, Rabat', '+212624567890'),
(14, 'Amina', 'Talbi', '<EMAIL>', 'Avenue Mohamed VI, Tangier', '+212625678901'),
(15, 'Mehdi', 'Hajji', '<EMAIL>', 'Quartier Hassan, Rabat', '+212626789012'),
(16, 'Rachida', 'Boukhari', '<EMAIL>', 'Rue Ibn Sina, Fès', '+212627890123'),
(17, 'Karim', 'Berrada', '<EMAIL>', 'Quartier Massira, Marrakech', '+212628901234'),
(18, 'Hanae', 'El Khatib', '<EMAIL>', 'Rue Oued Fès, Oujda', '+212629012345'),
(19, 'Mustapha', 'El Ghazali', '<EMAIL>', 'Boulevard Ziraoui, Casablanca', '+212630123456'),
(20, 'Meryem', 'Chafai', '<EMAIL>', 'Avenue Al Alaouiyine, Meknès', '+212631234567');

-- --------------------------------------------------------

--
-- Table structure for table `discount`
--

CREATE TABLE IF NOT EXISTS `discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `discount_type` enum('PERCENTAGE','VALUE') NOT NULL,
  `discount_value` float NOT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_207ea3c6100ab95ddd43b90ff88` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `inventory`
--

CREATE TABLE IF NOT EXISTS `inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quantity` int(11) NOT NULL,
  `last_updated` timestamp NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `variation_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_732fdb1f76432d65d2c136340dc` (`product_id`),
  KEY `FK_33f7c9dc1f52390aa417c4f77b4` (`variation_id`),
  KEY `FK_5e4d38ade6f246f20f468a7ad4b` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `kiosk`
--

CREATE TABLE IF NOT EXISTS `kiosk` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` enum('IN_SERVICE','OUT_OF_SERVICE','DISABLED') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_e2fd29b7aabd8cc510dadc0df3e` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=11 ;

--
-- Dumping data for table `kiosk`
--

INSERT INTO `kiosk` (`id`, `name`, `status`, `branch_id`) VALUES
(1, 'Downtown Self-Order Kiosk', 'IN_SERVICE', 1),
(2, 'Mall Food Court Kiosk', 'OUT_OF_SERVICE', 1),
(3, 'Drive-Thru Kiosk', 'DISABLED', 1),
(4, 'Airport Lounge Kiosk', 'IN_SERVICE', 1),
(5, 'City Center Kiosk', 'OUT_OF_SERVICE', 1),
(6, 'University Campus Kiosk', 'DISABLED', 1),
(7, 'Seaside Plaza Kiosk', 'IN_SERVICE', 1),
(8, 'Hotel Lobby Kiosk', 'OUT_OF_SERVICE', 1),
(9, 'Cinema Complex Kiosk', 'DISABLED', 1),
(10, 'Park Avenue Kiosk', 'IN_SERVICE', 1);

-- --------------------------------------------------------

--
-- Table structure for table `language`
--

CREATE TABLE IF NOT EXISTS `language` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_465b3173cdddf0ac2d3fe73a33` (`code`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=8 ;

--
-- Dumping data for table `language`
--

INSERT INTO `language` (`id`, `code`, `name`) VALUES
(1, 'EN', 'English'),
(2, 'FR', 'Français'),
(3, 'ES', 'Español'),
(4, 'IT', 'Italiano'),
(5, 'DE', 'Deutsch'),
(6, 'NL', 'Nederlands'),
(7, 'AR', 'العربية');

-- --------------------------------------------------------

--
-- Table structure for table `loyalty`
--

CREATE TABLE IF NOT EXISTS `loyalty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `loyalty_points` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_454c65be2b44f552b6766f2dcf1` (`customer_id`),
  KEY `FK_a5d772a026e06617161841c5664` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `notification`
--

CREATE TABLE IF NOT EXISTS `notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message` varchar(255) NOT NULL,
  `type` enum('SMS','WHATSAPP','EMAIL','PUSH') NOT NULL,
  `sent_at` timestamp NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_3ea5cd8a1de9cbf90c86dd0582c` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `option`
--

CREATE TABLE IF NOT EXISTS `option` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_f9bd0c5005ece46cbb3cd947462` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=25 ;

--
-- Dumping data for table `option`
--

INSERT INTO `option` (`id`, `name`, `branch_id`) VALUES
(8, 'Meat', 1),
(9, 'Sauces', 1),
(10, 'Size', 1),
(11, 'Cheese', 1),
(12, 'Sweetness', 1),
(13, 'Milk', 1),
(14, 'Caffeine', 1),
(15, 'Spice Level', 1),
(16, 'test en', 1),
(17, 'TEST 1', 2),
(18, 'TEST 2', 2),
(22, 'test 12', 1),
(23, 'test 12', NULL),
(24, 'test 123123', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `order`
--

CREATE TABLE IF NOT EXISTS `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(255) NOT NULL,
  `status` enum('PENDING','PREPARING','READY','COMPLETED','CANCELLED') NOT NULL,
  `type` enum('DINE_IN','TAKE_AWAY','DELIVERY') NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `customer_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `kiosk_id` int(11) DEFAULT NULL,
  `pos_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_f9180f384353c621e8d0c414c1` (`order_number`),
  KEY `FK_cd7812c96209c5bdd48a6b858b0` (`customer_id`),
  KEY `FK_bffc1bebf6db72c49d80df64b24` (`branch_id`),
  KEY `FK_a2b1fd48f82280f14b983066149` (`kiosk_id`),
  KEY `FK_b9ed44eb87f5c9df365aa5adf90` (`pos_id`),
  KEY `FK_baced9282892a60354aaa789fb4` (`coupon_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=61 ;

--
-- Dumping data for table `order`
--

INSERT INTO `order` (`id`, `order_number`, `status`, `type`, `created_at`, `customer_id`, `branch_id`, `kiosk_id`, `pos_id`, `coupon_id`) VALUES
(2, '1', 'COMPLETED', 'DINE_IN', '2025-04-09 16:27:29.903036', NULL, 1, NULL, 1, NULL),
(4, '2', 'COMPLETED', 'DINE_IN', '2025-04-09 17:07:39.228820', NULL, 1, NULL, 1, NULL),
(5, '3', 'COMPLETED', 'DINE_IN', '2025-04-09 18:04:14.385459', NULL, 1, NULL, 1, NULL),
(6, '4', 'COMPLETED', 'DINE_IN', '2025-04-09 18:05:55.519616', NULL, 1, NULL, 1, NULL),
(7, '5', 'COMPLETED', 'DINE_IN', '2025-04-10 13:25:49.416335', NULL, 1, NULL, 1, NULL),
(8, '6', 'COMPLETED', 'DINE_IN', '2025-04-10 13:52:12.498022', NULL, 1, NULL, 1, NULL),
(9, '7', 'COMPLETED', 'DINE_IN', '2025-04-10 13:52:36.100929', NULL, 1, NULL, 1, NULL),
(10, '8', 'COMPLETED', 'DINE_IN', '2025-04-10 13:53:03.244815', NULL, 1, NULL, 1, NULL),
(11, '9', 'COMPLETED', 'DINE_IN', '2025-04-10 13:53:27.032491', NULL, 1, NULL, 1, NULL),
(12, '10', 'PREPARING', 'DINE_IN', '2025-04-10 13:54:57.206665', NULL, 1, NULL, 1, NULL),
(24, '11', 'READY', 'DINE_IN', '2025-04-10 14:10:25.340171', NULL, 1, NULL, 1, NULL),
(27, '12', 'READY', 'DINE_IN', '2025-04-10 14:17:45.269435', NULL, 1, NULL, 1, NULL),
(28, '13', 'READY', 'DINE_IN', '2025-04-10 14:26:15.904450', NULL, 1, NULL, 1, NULL),
(32, '14', 'READY', 'DINE_IN', '2025-04-10 14:35:52.226785', NULL, 1, NULL, 1, NULL),
(33, '15', 'READY', 'DINE_IN', '2025-04-10 14:35:56.340646', NULL, 1, NULL, 1, NULL),
(34, '16', 'PREPARING', 'DINE_IN', '2025-04-10 14:35:59.118667', NULL, 1, NULL, 1, NULL),
(35, '17', 'PREPARING', 'DINE_IN', '2025-04-10 14:36:01.651200', NULL, 1, NULL, 1, NULL),
(36, '18', 'PREPARING', 'DINE_IN', '2025-04-10 14:36:04.344445', NULL, 1, NULL, 1, NULL),
(37, '19', 'PREPARING', 'DINE_IN', '2025-04-10 14:36:06.877624', NULL, 1, NULL, 1, NULL),
(38, '20', 'READY', 'DINE_IN', '2025-04-10 14:36:09.438248', NULL, 1, NULL, 1, NULL),
(39, '21', 'COMPLETED', 'DINE_IN', '2025-04-10 14:36:11.989099', NULL, 1, NULL, 1, NULL),
(40, '22', 'READY', 'DINE_IN', '2025-04-10 14:36:14.659148', NULL, 1, NULL, 1, NULL),
(41, '23', 'COMPLETED', 'DINE_IN', '2025-04-10 16:19:36.352233', NULL, 1, NULL, 1, NULL),
(42, '24', 'PENDING', 'DINE_IN', '2025-04-10 17:32:42.791802', NULL, 1, NULL, 1, NULL),
(43, '25', 'COMPLETED', 'TAKE_AWAY', '2025-04-11 11:32:13.272376', NULL, 1, NULL, 1, NULL),
(44, '26', 'COMPLETED', 'DINE_IN', '2025-04-11 11:56:02.289214', NULL, 1, NULL, 1, NULL),
(45, '27', 'COMPLETED', 'DINE_IN', '2025-04-11 11:58:24.460370', NULL, 1, NULL, 1, NULL),
(46, '28', 'READY', 'TAKE_AWAY', '2025-04-11 15:02:48.766818', NULL, 1, NULL, 1, NULL),
(47, '29', 'PENDING', 'DINE_IN', '2025-04-11 15:23:39.773427', NULL, 1, NULL, 1, NULL),
(48, '30', 'PENDING', 'DINE_IN', '2025-04-11 15:24:21.032456', NULL, 1, NULL, 1, NULL),
(49, '31', 'PENDING', 'DINE_IN', '2025-04-11 15:25:04.925182', NULL, 1, NULL, 1, NULL),
(50, '32', 'READY', 'DINE_IN', '2025-04-11 15:25:23.595889', NULL, 1, NULL, 1, NULL),
(51, '33', 'PENDING', 'TAKE_AWAY', '2025-04-11 15:25:51.464048', NULL, 1, NULL, 1, NULL),
(52, '34', 'PENDING', 'TAKE_AWAY', '2025-04-11 15:26:15.367509', NULL, 1, NULL, 1, NULL),
(53, '35', 'READY', 'TAKE_AWAY', '2025-04-11 15:34:24.777411', NULL, 1, NULL, 1, NULL),
(54, '36', 'PENDING', 'TAKE_AWAY', '2025-04-11 16:32:52.011172', NULL, 1, NULL, 1, NULL),
(55, '37', 'PENDING', 'TAKE_AWAY', '2025-04-11 16:35:10.125232', NULL, 1, NULL, 1, NULL),
(56, '38', 'PENDING', 'DINE_IN', '2025-04-15 09:20:46.648967', NULL, 1, NULL, 1, NULL),
(57, '39', 'COMPLETED', 'DINE_IN', '2025-04-18 16:21:50.266353', NULL, 1, NULL, 1, NULL),
(58, '40', 'COMPLETED', 'DINE_IN', '2025-04-18 17:50:08.552960', NULL, 1, NULL, 1, NULL),
(60, '250', 'COMPLETED', 'TAKE_AWAY', '2025-04-11 11:32:13.272376', NULL, 2, NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `order_category_summary`
--

CREATE TABLE IF NOT EXISTS `order_category_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `total_amount` float NOT NULL,
  `total_orders` int(11) NOT NULL,
  `total_items_sold` int(11) NOT NULL,
  `total_discounts` float NOT NULL,
  `total_taxes` float NOT NULL,
  `change_percentage` float NOT NULL,
  `period` enum('DAILY','MONTHLY','YEARLY') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_e3af1acf28cf3c28f7d56b6b611` (`branch_id`),
  KEY `FK_e35a85c356c196e7bc47a9533ee` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `order_detail`
--

CREATE TABLE IF NOT EXISTS `order_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quantity` int(11) NOT NULL,
  `price` float NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `variation_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_985d5f728e1eebe4a3eabc43aac` (`product_id`),
  KEY `FK_264cb1d910f77be80066a7591d2` (`variation_id`),
  KEY `FK_a6ac5c99b8c02bd4ee53d3785be` (`order_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=177 ;

--
-- Dumping data for table `order_detail`
--

INSERT INTO `order_detail` (`id`, `quantity`, `price`, `product_id`, `variation_id`, `order_id`) VALUES
(3, 1, 12, 13, NULL, 2),
(4, 1, 20, 16, NULL, 2),
(5, 2, 12, 13, NULL, 4),
(6, 1, 12, 25, NULL, 4),
(7, 1, 25, 11, NULL, 4),
(8, 1, 5, 11, 28, 4),
(9, 1, 0, 11, 30, 4),
(10, 1, 0, 11, 31, 4),
(11, 1, 2, 11, 24, 4),
(12, 1, 0, 11, 36, 4),
(13, 1, 25, 11, NULL, 5),
(14, 1, 0, 11, 27, 5),
(15, 1, 0, 11, 30, 5),
(16, 1, 2, 11, 24, 5),
(17, 1, 0, 11, 36, 5),
(18, 1, 25, 11, NULL, 6),
(19, 1, 0, 11, 27, 6),
(20, 1, 0, 11, 30, 6),
(21, 1, 2, 11, 24, 6),
(22, 1, 0, 11, 36, 6),
(23, 1, 12, 13, NULL, 7),
(24, 1, 12, 13, NULL, 8),
(25, 1, 12, 13, NULL, 8),
(26, 1, 12, 25, NULL, 8),
(27, 1, 12, 27, NULL, 8),
(28, 1, 20, 16, NULL, 9),
(29, 1, 25, 18, NULL, 9),
(30, 1, 12, 24, NULL, 9),
(31, 1, 12, 10, NULL, 10),
(32, 1, 5, 10, 28, 10),
(33, 1, 60, 23, NULL, 10),
(34, 1, 6, 15, NULL, 11),
(35, 1, 35, 19, NULL, 11),
(36, 1, 15, 26, NULL, 11),
(37, 1, 6, 15, NULL, 12),
(38, 1, 35, 19, NULL, 12),
(39, 1, 15, 26, NULL, 12),
(40, 1, 45, 20, NULL, 12),
(41, 1, 12, 25, NULL, 12),
(42, 1, 12, 24, NULL, 12),
(43, 1, 12, 24, NULL, 24),
(44, 1, 12, 25, NULL, 24),
(45, 1, 12, 24, NULL, 27),
(46, 1, 12, 25, NULL, 27),
(47, 1, 12, 24, NULL, 28),
(48, 1, 12, 25, NULL, 28),
(49, 1, 12, 24, NULL, 32),
(50, 1, 12, 25, NULL, 32),
(51, 1, 12, 24, NULL, 33),
(52, 1, 12, 25, NULL, 33),
(53, 1, 12, 24, NULL, 34),
(54, 1, 12, 25, NULL, 34),
(55, 1, 12, 24, NULL, 35),
(56, 1, 12, 25, NULL, 35),
(57, 1, 12, 24, NULL, 36),
(58, 1, 12, 25, NULL, 36),
(59, 1, 12, 24, NULL, 37),
(60, 1, 12, 25, NULL, 37),
(61, 1, 12, 24, NULL, 38),
(62, 1, 12, 25, NULL, 38),
(63, 1, 12, 24, NULL, 39),
(64, 1, 12, 25, NULL, 39),
(65, 1, 12, 24, NULL, 40),
(66, 1, 12, 25, NULL, 40),
(67, 1, 12, 25, NULL, 41),
(68, 1, 12, 27, NULL, 41),
(69, 1, 12, 24, NULL, 41),
(70, 1, 35, 19, NULL, 42),
(71, 2, 25, 18, NULL, 43),
(72, 1, 25, 11, NULL, 44),
(73, 1, 0, 11, 27, 44),
(74, 1, 0, 11, 30, 44),
(75, 1, 0, 11, 36, 44),
(76, 1, 6, 11, 26, 44),
(77, 1, 12, 13, NULL, 45),
(78, 1, 12, 10, NULL, 46),
(79, 1, 20, 10, 29, 46),
(80, 1, 25, 11, NULL, 46),
(81, 1, 5, 11, 28, 46),
(82, 1, 0, 11, 30, 46),
(83, 1, 0, 11, 31, 46),
(84, 1, 2, 11, 24, 46),
(85, 1, 4, 11, 25, 46),
(86, 1, 0, 11, 36, 46),
(87, 1, 12, 13, NULL, 46),
(88, 1, 12, 25, NULL, 46),
(89, 1, 12, 24, NULL, 46),
(90, 1, 12, 10, NULL, 47),
(91, 1, 5, 10, 28, 47),
(92, 1, 50, 21, NULL, 47),
(93, 1, 12, 13, NULL, 47),
(94, 1, 12, 25, NULL, 47),
(95, 1, 12, 24, NULL, 47),
(96, 1, 12, 27, NULL, 47),
(97, 2, 6, 15, NULL, 47),
(98, 3, 6, 15, NULL, 48),
(99, 1, 35, 19, NULL, 48),
(100, 1, 15, 26, NULL, 48),
(101, 1, 35, 19, NULL, 48),
(102, 1, 12, 10, NULL, 48),
(103, 1, 5, 10, 28, 48),
(104, 1, 55, 22, NULL, 48),
(105, 1, 60, 23, NULL, 48),
(106, 1, 12, 27, NULL, 48),
(107, 1, 12, 25, NULL, 48),
(108, 1, 25, 11, NULL, 49),
(109, 1, 0, 11, 27, 49),
(110, 1, 0, 11, 30, 49),
(111, 1, 2, 11, 24, 49),
(112, 1, 0, 11, 36, 49),
(113, 1, 12, 25, NULL, 49),
(114, 1, 12, 13, NULL, 49),
(115, 1, 12, 27, NULL, 49),
(116, 1, 20, 16, NULL, 49),
(117, 1, 12, 24, NULL, 49),
(118, 2, 6, 15, NULL, 49),
(119, 2, 6, 15, NULL, 50),
(120, 1, 35, 19, NULL, 50),
(121, 1, 12, 24, NULL, 50),
(122, 1, 18, 17, NULL, 50),
(123, 1, 12, 25, NULL, 50),
(124, 1, 45, 20, NULL, 51),
(125, 1, 50, 21, NULL, 51),
(126, 1, 35, 19, NULL, 51),
(127, 2, 6, 15, NULL, 51),
(128, 1, 15, 26, NULL, 51),
(129, 1, 12, 27, NULL, 51),
(130, 1, 20, 16, NULL, 51),
(131, 1, 12, 13, NULL, 51),
(132, 1, 25, 11, NULL, 52),
(133, 1, 5, 11, 28, 52),
(134, 1, 0, 11, 30, 52),
(135, 1, 0, 11, 31, 52),
(136, 1, 2, 11, 24, 52),
(137, 1, 0, 11, 36, 52),
(138, 1, 60, 23, NULL, 52),
(139, 1, 20, 16, NULL, 52),
(140, 1, 12, 25, NULL, 52),
(141, 1, 12, 27, NULL, 52),
(142, 1, 12, 24, NULL, 52),
(143, 1, 18, 17, NULL, 52),
(144, 1, 12, 27, NULL, 53),
(145, 1, 12, 25, NULL, 53),
(146, 1, 12, 13, NULL, 54),
(147, 1, 12, 10, NULL, 54),
(148, 1, 5, 10, 28, 54),
(149, 1, 25, 11, NULL, 54),
(150, 1, 5, 11, 28, 54),
(151, 1, 0, 11, 30, 54),
(152, 1, 0, 11, 31, 54),
(153, 1, 2, 11, 24, 54),
(154, 1, 0, 11, 36, 54),
(155, 1, 12, 27, NULL, 54),
(156, 1, 25, 11, NULL, 55),
(157, 1, 5, 11, 28, 55),
(158, 1, 0, 11, 30, 55),
(159, 1, 0, 11, 31, 55),
(160, 1, 2, 11, 24, 55),
(161, 1, 0, 11, 36, 55),
(162, 1, 12, 25, NULL, 55),
(163, 1, 12, 27, NULL, 55),
(164, 1, 12, 13, NULL, 55),
(165, 1, 12, 24, NULL, 55),
(166, 1, 12, 13, NULL, 56),
(167, 1, 12, 25, NULL, 56),
(168, 3, 12, 27, NULL, 56),
(169, 1, 12, 13, NULL, 57),
(170, 1, 12, 25, NULL, 57),
(171, 1, 12, 10, NULL, 57),
(172, 1, 5, 10, 28, 57),
(173, 1, 12, 13, NULL, 58),
(174, 1, 12, 25, NULL, 58),
(175, 1, 12, 10, NULL, 58),
(176, 1, 5, 10, 28, 58);

-- --------------------------------------------------------

--
-- Table structure for table `order_summary`
--

CREATE TABLE IF NOT EXISTS `order_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `total_amount` float NOT NULL,
  `total_orders` int(11) NOT NULL,
  `total_items_sold` int(11) NOT NULL,
  `total_discounts` float NOT NULL,
  `total_taxes` float NOT NULL,
  `total_coupons` int(11) NOT NULL,
  `average_order_value` float NOT NULL,
  `change_percentage` float NOT NULL,
  `period` enum('DAILY','MONTHLY','YEARLY') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_9fe12bd3f473edb844707d24fa6` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `payment`
--

CREATE TABLE IF NOT EXISTS `payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `amount` float NOT NULL,
  `status` enum('PENDING','COMPLETED','FAILED') NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `order_id` int(11) DEFAULT NULL,
  `payment_method_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `REL_f5221735ace059250daac9d980` (`order_id`),
  KEY `FK_365af7f69f9142427cf30395b00` (`payment_method_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=41 ;

--
-- Dumping data for table `payment`
--

INSERT INTO `payment` (`id`, `amount`, `status`, `created_at`, `order_id`, `payment_method_id`) VALUES
(1, 32, 'PENDING', '2025-04-09 16:27:29.916950', 2, 9),
(2, 68, 'PENDING', '2025-04-09 17:07:39.262668', 4, 9),
(3, 0, 'PENDING', '2025-04-09 18:04:14.433165', 5, 9),
(4, 27, 'PENDING', '2025-04-09 18:05:55.549110', 6, 9),
(5, 12, 'PENDING', '2025-04-10 13:25:49.451496', 7, 9),
(6, 12, 'PENDING', '2025-04-10 13:52:12.559766', 8, 9),
(7, 57, 'PENDING', '2025-04-10 13:52:36.124694', 9, 9),
(8, 77, 'PENDING', '2025-04-10 13:53:03.273623', 10, 9),
(9, 56, 'PENDING', '2025-04-10 13:53:27.067753', 11, 9),
(10, 125, 'PENDING', '2025-04-10 13:53:57.259509', 12, 9),
(11, 24, 'PENDING', '2025-04-10 14:10:25.368281', 24, 9),
(12, 24, 'PENDING', '2025-04-10 14:17:45.309317', 27, 9),
(13, 24, 'PENDING', '2025-04-10 14:26:15.936579', 28, 9),
(14, 24, 'PENDING', '2025-04-10 14:35:52.266363', 32, 9),
(15, 24, 'PENDING', '2025-04-10 14:35:56.356787', 33, 9),
(16, 24, 'PENDING', '2025-04-10 14:35:59.134214', 34, 9),
(17, 24, 'PENDING', '2025-04-10 14:36:01.661762', 35, 9),
(18, 24, 'PENDING', '2025-04-10 14:36:04.359390', 36, 9),
(19, 24, 'PENDING', '2025-04-10 14:36:06.892521', 37, 9),
(20, 24, 'PENDING', '2025-04-10 14:36:09.451317', 38, 9),
(21, 24, 'PENDING', '2025-04-10 14:36:12.002108', 39, 9),
(22, 24, 'PENDING', '2025-04-10 14:36:14.676135', 40, 9),
(23, 36, 'PENDING', '2025-04-10 16:19:36.380523', 41, 9),
(24, 35, 'PENDING', '2025-04-10 17:32:42.803729', 42, 9),
(25, 50, 'PENDING', '2025-04-11 11:32:13.291316', 43, 9),
(26, 31, 'PENDING', '2025-04-11 11:56:02.321489', 44, 9),
(27, 12, 'PENDING', '2025-04-11 11:58:24.470557', 45, 9),
(28, 104, 'PENDING', '2025-04-11 15:02:48.822396', 46, 9),
(29, 127, 'PENDING', '2025-04-11 15:23:39.797366', 47, 9),
(30, 259, 'PENDING', '2025-04-11 15:24:21.069751', 48, 9),
(31, 107, 'PENDING', '2025-04-11 15:25:05.000734', 49, 9),
(32, 89, 'PENDING', '2025-04-11 15:25:23.608420', 50, 9),
(33, 201, 'PENDING', '2025-04-11 15:25:51.485085', 51, 9),
(34, 0, 'PENDING', '2025-04-11 15:26:15.394228', 52, 9),
(35, 0, 'PENDING', '2025-04-11 15:34:24.788785', 53, 9),
(36, 73, 'PENDING', '2025-04-11 16:32:52.042409', 54, 9),
(37, 80, 'PENDING', '2025-04-11 16:35:10.161292', 55, 9),
(38, 60, 'PENDING', '2025-04-15 09:20:46.747409', 56, 9),
(39, 41, 'PENDING', '2025-04-18 16:21:50.305332', 57, 9),
(40, 41, 'PENDING', '2025-04-18 17:50:08.585466', 58, 9);

-- --------------------------------------------------------

--
-- Table structure for table `payment_method`
--

CREATE TABLE IF NOT EXISTS `payment_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(4) NOT NULL DEFAULT '1',
  `description` varchar(255) DEFAULT NULL,
  `transaction_fees` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_6101666760258a840e115e1bb1` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=15 ;

--
-- Dumping data for table `payment_method`
--

INSERT INTO `payment_method` (`id`, `name`, `is_active`, `description`, `transaction_fees`) VALUES
(8, 'Carte de Crédit', 1, 'Paiements effectués avec Visa, Mastercard ou autres cartes de crédit.', 2.5),
(9, 'Espèces', 1, 'Paiements en espèces au point de vente.', 0),
(10, 'Apple Pay', 1, 'Paiements avec Apple Pay sur des appareils compatibles.', 1.5),
(11, 'Google Pay', 1, 'Paiements avec Google Pay sur des appareils compatibles.', 1.5),
(12, 'Carte Cadeau', 1, 'Paiements avec des cartes cadeaux préchargées.', 0),
(13, 'Chèque', 0, 'Paiements par chèques écrits.', 0.5),
(14, 'Argent Mobile', 1, 'Paiements via des plateformes de transfert d’argent mobile.', 1);

-- --------------------------------------------------------

--
-- Table structure for table `pos`
--

CREATE TABLE IF NOT EXISTS `pos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` enum('IN_SERVICE','OUT_OF_SERVICE','DISABLED') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_287bdf629337d91b27b74f6e148` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=2 ;

--
-- Dumping data for table `pos`
--

INSERT INTO `pos` (`id`, `name`, `status`, `branch_id`) VALUES
(1, 'point of sale', 'IN_SERVICE', 1);

-- --------------------------------------------------------

--
-- Table structure for table `pos_session`
--

CREATE TABLE IF NOT EXISTS `pos_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `open_at` timestamp NULL DEFAULT NULL,
  `closed_at` timestamp NULL DEFAULT NULL,
  `status` enum('ACTIVE','CLOSED') NOT NULL,
  `pos_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_dcb1a6a83f4aa4a9f539ffccce1` (`pos_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `product`
--

CREATE TABLE IF NOT EXISTS `product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `price` float NOT NULL,
  `availability_status` tinyint(4) NOT NULL DEFAULT '1',
  `available_from` varchar(255) DEFAULT NULL,
  `available_to` varchar(255) DEFAULT NULL,
  `is_addon` tinyint(4) NOT NULL DEFAULT '0',
  `is_inventory_tracked` tinyint(4) NOT NULL DEFAULT '1',
  `image_url` varchar(255) DEFAULT 'product.png',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `modified_at` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deleted_at` datetime(6) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `unit_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_0dce9bc93c2d2c399982d04bef1` (`category_id`),
  KEY `FK_b15422982adca3bf53adfb535de` (`unit_id`),
  KEY `FK_47ec9f981fac28851de1d6bd8db` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=33 ;

--
-- Dumping data for table `product`
--

INSERT INTO `product` (`id`, `name`, `description`, `price`, `availability_status`, `available_from`, `available_to`, `is_addon`, `is_inventory_tracked`, `image_url`, `created_at`, `modified_at`, `deleted_at`, `category_id`, `unit_id`, `branch_id`) VALUES
(10, 'Pizza', 'Pizza', 12, 1, '15:25', '23:30', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/1db2d25fa756a210bc23c2a94391054d4e.jpg', '2024-10-25 14:25:55.339266', '2025-04-22 14:45:48.141747', NULL, 13, 8, 1),
(11, 'Tacos', 'Tacos', 25, 1, '15:30', '21:30', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/d9d9d103db42bd33e3c82a4f75a2a610610.png', '2024-10-25 14:31:00.270545', '2025-04-22 14:46:05.829796', NULL, 17, 8, 1),
(13, 'Fanta', 'Fanta', 12, 1, '17:53', '22:53', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/f104a106c921a7cc13cf5af6c19ce106a19.png', '2024-10-25 16:53:46.547500', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(15, 'Frites', 'Frites', 6, 1, '14:17', '20:17', 1, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/3ea106ad0cec8df638e915c98869cdfad.png', '2024-11-01 13:19:30.943279', '2025-04-22 14:46:05.829796', NULL, 12, 8, 1),
(16, 'Mojito', 'Mojito', 20, 1, '14:51', '20:51', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/b27884135ec910ff5feb8275ce2b7b343.png', '2024-11-01 13:51:44.705544', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(17, 'Iced Latte', 'Iced Latte', 18, 1, '15:52', '20:52', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/7368ccd4bac6d4369795695533fcf7cb.png', '2024-11-01 13:52:31.872425', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(18, 'Smoothie', 'Smoothie', 25, 1, '14:53', '20:53', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/a41039127008f1121710072d10153283acc.jpg', '2024-11-01 13:53:28.904757', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(19, 'Club Sandwich', 'Club Sandwich', 35, 1, '14:54', '20:54', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/2e45f2952b41db900a8d284ac10d6b8eb.png', '2024-11-01 13:54:16.728743', '2025-04-22 14:46:05.829796', NULL, 12, 8, 1),
(20, 'Margherita', 'Margherita', 45, 1, '14:56', '20:56', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/e34b85ad756c63dec945e6f6e872b5a1.png', '2024-11-01 13:57:05.115554', '2025-04-22 14:46:05.829796', NULL, 13, 8, 1),
(21, 'Pepperoni', 'Pepperoni', 50, 1, '14:57', '20:57', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/93d55a75fa71c5c88297921d33366826.png', '2024-11-01 13:58:26.241350', '2025-04-22 14:46:05.829796', NULL, 13, 8, 1),
(22, 'BBQ Chicken', 'BBQ Chicken', 55, 1, '14:58', '20:58', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/e894a7abc1acbdd112ea68bc8e4d8ec8.png', '2024-11-01 13:59:12.671357', '2025-04-22 14:46:05.829796', NULL, 13, 8, 1),
(23, 'Tagine', 'Tagine', 60, 1, '16:00', '22:00', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/7425438bb61274aab1ce8d246f1eb852.png', '2024-11-01 14:00:38.312032', '2025-04-22 14:46:05.829796', NULL, 15, 8, 1),
(24, 'Coca Cola', 'Coca Cola', 12, 1, '15:13', '21:13', 1, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/f33f7fc9ca27022c8e0fb347e4aef7de.png', '2024-11-01 14:13:26.639321', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(25, 'Pepsi', 'Pepsi', 12, 1, '15:14', '21:14', 1, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/25661a027c3dacbf7e48ec637435d49f.png', '2024-11-01 14:14:51.528035', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(26, 'Lava Cake', 'Lava Cake', 15, 1, '15:18', '21:18', 1, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/fe4cacff6910ca53130d9b00538038286.jpg', '2024-11-01 14:18:11.148970', '2025-04-22 14:46:05.829796', NULL, 12, 8, 1),
(27, 'Iced Tea', 'Iced Tea', 12, 1, '15:19', '22:19', 1, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/b120378f58da699f10bbeaae9cc35ab27.png', '2024-11-01 14:19:19.761912', '2025-04-22 14:46:05.829796', NULL, 11, 10, 1),
(28, 'test', 'test', 20, 1, '15:04', '16:05', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/product.png', '2025-04-21 15:05:05.285809', '2025-04-22 14:52:42.537683', NULL, 11, 1, 2),
(29, 'test1', 'test1', 20, 1, '15:04', '16:05', 0, 1, 'https://vmi1015553.contaboserver.net:6061/uploads/product.png', '2025-04-22 16:48:56.389276', '2025-04-22 15:48:56.389276', NULL, 26, 1, 2);

-- --------------------------------------------------------

--
-- Table structure for table `product_discounts`
--

CREATE TABLE IF NOT EXISTS `product_discounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `discount_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_68e668b0341f0276ebcc2a91506` (`product_id`),
  KEY `FK_4d3f74396271c6bee81a1a547a0` (`discount_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `product_option`
--

CREATE TABLE IF NOT EXISTS `product_option` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `max_allowed` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `option_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_e634fca34f6b594b87fdbee95f6` (`product_id`),
  KEY `FK_9f53e0e9868b4d64b048bff8701` (`option_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=32 ;

--
-- Dumping data for table `product_option`
--

INSERT INTO `product_option` (`id`, `max_allowed`, `product_id`, `option_id`) VALUES
(21, 1, 11, 10),
(22, 2, 11, 11),
(23, 1, 10, 10),
(24, 2, 11, 8),
(25, 1, 11, 9);

-- --------------------------------------------------------

--
-- Table structure for table `product_taxes`
--

CREATE TABLE IF NOT EXISTS `product_taxes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `tax_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_1d39e319e41b1edce44933cfe75` (`product_id`),
  KEY `FK_db8c22b3e1e26adae58c92dc64e` (`tax_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `product_variations`
--

CREATE TABLE IF NOT EXISTS `product_variations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `price` float NOT NULL,
  `availability_status` tinyint(4) NOT NULL DEFAULT '1',
  `product_id` int(11) DEFAULT NULL,
  `variation_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_318ea43851e4dcc507601cc9f08` (`product_id`),
  KEY `FK_85acd982176dfe61b299cd8da68` (`variation_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=49 ;

--
-- Dumping data for table `product_variations`
--

INSERT INTO `product_variations` (`id`, `price`, `availability_status`, `product_id`, `variation_id`) VALUES
(28, 0, 1, 11, 27),
(29, 5, 1, 11, 28),
(30, 10, 1, 11, 29),
(31, 0, 1, 11, 30),
(32, 0, 1, 11, 31),
(33, 0, 1, 11, 32),
(34, 0, 1, 10, 27),
(35, 5, 1, 10, 28),
(36, 20, 1, 10, 29),
(37, 2, 1, 11, 24),
(38, 6, 1, 11, 26),
(39, 4, 1, 11, 25),
(40, 0, 1, 11, 36);

-- --------------------------------------------------------

--
-- Table structure for table `purchase`
--

CREATE TABLE IF NOT EXISTS `purchase` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_number` varchar(255) NOT NULL,
  `purchase_date` timestamp NOT NULL,
  `total_amount` float NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `supplier_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_4763c61264d54673ee262accbe` (`purchase_number`),
  KEY `FK_ac1a50d78592b601510bd8a0e53` (`branch_id`),
  KEY `FK_8d9a856657c46725d085c73e4fc` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `purchase_details`
--

CREATE TABLE IF NOT EXISTS `purchase_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quantity` int(11) NOT NULL,
  `price` float NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `variation_id` int(11) DEFAULT NULL,
  `purchase_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_802cd8f2a3c2e09932fc1bfad88` (`product_id`),
  KEY `FK_972c5d1577048dfa45128ed03d4` (`variation_id`),
  KEY `FK_7769941f7424a0030e1f6c7b7d3` (`purchase_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `purchase_summary`
--

CREATE TABLE IF NOT EXISTS `purchase_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `total_amount` float NOT NULL,
  `total_purchases` int(11) NOT NULL,
  `total_items_purchased` int(11) NOT NULL,
  `change_percentage` float NOT NULL,
  `period` enum('DAILY','MONTHLY','YEARLY') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_784a4323a220278d58eeeae3ae1` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `store`
--

CREATE TABLE IF NOT EXISTS `store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `logo_url` varchar(255) DEFAULT 'store.png',
  `website` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=3 ;

--
-- Dumping data for table `store`
--

INSERT INTO `store` (`id`, `name`, `address`, `phone`, `email`, `logo_url`, `website`) VALUES
(1, 'Espace360', 'France', '', '<EMAIL>', 'store.png', 'www.espace360.fr'),
(2, 'O''slo', 'France', '', '<EMAIL>', 'store.png', 'www.o-slo.fr');

-- --------------------------------------------------------

--
-- Table structure for table `supplier`
--

CREATE TABLE IF NOT EXISTS `supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `logo_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_c40cbff7400f06ae1c8d9f4233` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `tax`
--

CREATE TABLE IF NOT EXISTS `tax` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `tax_rate` float NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `tax_category`
--

CREATE TABLE IF NOT EXISTS `tax_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tax_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_4bf319f5d3986cbb0584de97073` (`tax_id`),
  KEY `FK_7fdc0186b0a90d5b2172e53d6ca` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `tax_product`
--

CREATE TABLE IF NOT EXISTS `tax_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tax_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_e86258e270cc3ac2d1378d5e4a5` (`tax_id`),
  KEY `FK_cc9411ac13a4f1bd0f70f960c34` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `tax_store`
--

CREATE TABLE IF NOT EXISTS `tax_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tax_id` int(11) DEFAULT NULL,
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_e096f75ac0aa71ab309034ab38a` (`tax_id`),
  KEY `FK_e74095e2d518dab6aa7a265b26a` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Table structure for table `translation`
--

CREATE TABLE IF NOT EXISTS `translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translated_name` varchar(255) NOT NULL,
  `language_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `option_id` int(11) DEFAULT NULL,
  `variation_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_39403d84a9575d02f0c4bcdef32` (`language_id`),
  KEY `FK_1c14f78e719ce89380c7680841c` (`option_id`),
  KEY `FK_f23f2870f14fc46735f85869bee` (`product_id`),
  KEY `FK_3b4a58c7173cadb208b333e50c1` (`variation_id`),
  KEY `FK_eb51c35ae5b9d90a24939049260` (`category_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=89 ;

--
-- Dumping data for table `translation`
--

INSERT INTO `translation` (`id`, `translated_name`, `language_id`, `product_id`, `option_id`, `variation_id`, `category_id`) VALUES
(1, 'بيتزا', 7, 10, NULL, NULL, NULL),
(2, 'تاكو', 7, 11, NULL, NULL, NULL),
(3, 'فانتا', 7, 13, NULL, NULL, NULL),
(4, 'مشروبات', 7, NULL, NULL, NULL, 11),
(5, 'ساندويتشات', 7, NULL, NULL, NULL, 12),
(6, 'سلطات', 7, NULL, NULL, NULL, 14),
(7, 'أطباق تقليدية', 7, NULL, NULL, NULL, 15),
(8, 'أومليت', 7, NULL, NULL, NULL, 16),
(9, 'لحم', 7, NULL, 8, NULL, NULL),
(10, 'صلصات', 7, NULL, 9, NULL, NULL),
(11, 'حجم', 7, NULL, 10, NULL, NULL),
(12, 'جبن', 7, NULL, 11, NULL, NULL),
(13, 'حلاوة', 7, NULL, 12, NULL, NULL),
(14, 'حليب', 7, NULL, 13, NULL, NULL),
(15, 'كافيين', 7, NULL, 14, NULL, NULL),
(16, 'مستوى التوابل', 7, NULL, 15, NULL, NULL),
(17, 'لحم بقري', 7, NULL, NULL, 24, NULL),
(18, 'ديك رومي', 7, NULL, NULL, 25, NULL),
(19, 'لحم خنزير', 7, NULL, NULL, 26, NULL),
(20, 'صغير', 7, NULL, NULL, 27, NULL),
(21, 'متوسط', 7, NULL, NULL, 28, NULL),
(22, 'كبير', 7, NULL, NULL, 29, NULL),
(23, 'جودة', 7, NULL, NULL, 30, NULL),
(24, 'شيدر', 7, NULL, NULL, 31, NULL),
(25, 'إدام', 7, NULL, NULL, 32, NULL),
(26, 'بدون سكر', 7, NULL, NULL, 33, NULL),
(27, 'عادي', 7, NULL, NULL, 34, NULL),
(28, 'حلو جدًا', 7, NULL, NULL, 35, NULL),
(29, 'خردل', 7, NULL, NULL, 36, NULL),
(30, 'لوز', 7, NULL, NULL, 37, NULL),
(31, 'صويا', 7, NULL, NULL, 38, NULL),
(33, 'شوفان', 7, NULL, NULL, 40, NULL),
(34, 'بيتزا', 7, NULL, NULL, NULL, 13),
(35, 'تاكو', 7, NULL, NULL, NULL, 17),
(36, 'Drinks', 1, NULL, NULL, NULL, 11),
(54, 'Boissons', 2, NULL, NULL, NULL, 11),
(55, 'Sandwiches', 1, NULL, NULL, NULL, 12),
(56, 'Sandwichs', 2, NULL, NULL, NULL, 12),
(57, 'Pizza''s', 1, NULL, NULL, NULL, 13),
(58, 'Pizza''s', 2, NULL, NULL, NULL, 13),
(59, 'Salads', 1, NULL, NULL, NULL, 14),
(60, 'Salades', 2, NULL, NULL, NULL, 14),
(61, 'Traditional', 1, NULL, NULL, NULL, 15),
(62, 'Traditionnel', 2, NULL, NULL, NULL, 15),
(63, 'Omlettes', 1, NULL, NULL, NULL, 16),
(64, 'Omelettes', 2, NULL, NULL, NULL, 16),
(65, 'Tacos', 1, NULL, NULL, NULL, 17),
(66, 'Tacos', 2, NULL, NULL, NULL, 17),
(67, 'Tacos', 1, 11, NULL, NULL, NULL),
(68, 'Tacos', 2, 11, NULL, NULL, NULL),
(69, 'Burgers', 1, NULL, NULL, NULL, 20),
(70, 'Desserts', 1, NULL, NULL, NULL, 21),
(71, 'Sushi', 1, NULL, NULL, NULL, 22),
(72, 'Pasta', 1, NULL, NULL, NULL, 23),
(73, 'Vegetarian', 1, NULL, NULL, NULL, 24),
(74, 'Breakfast', 1, NULL, NULL, NULL, 25),
(75, 'test', 1, 28, NULL, NULL, NULL),
(78, 'test 12', 1, NULL, 22, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `unit`
--

CREATE TABLE IF NOT EXISTS `unit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `abbreviation` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=14 ;

--
-- Dumping data for table `unit`
--

INSERT INTO `unit` (`id`, `name`, `abbreviation`) VALUES
(1, 'Kilogram', 'km'),
(8, 'Gram', 'g'),
(9, 'Liter', 'L'),
(10, 'Milliliter', 'mL'),
(11, 'Ounce', 'oz'),
(12, 'Pound', 'lb'),
(13, 'Percentage', '%');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `login` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `password` varchar(60) NOT NULL,
  `activated` tinyint(4) NOT NULL DEFAULT '1',
  `pos_code` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_a62473490b3e4578fd683235c5` (`login`),
  UNIQUE KEY `IDX_e12875dfb3b1d92d7d7c5377e2` (`email`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=27 ;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `login`, `first_name`, `last_name`, `email`, `image_url`, `password`, `activated`, `pos_code`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'admin', 'admin', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '0000', '2025-04-08 00:00:00.000000', '2025-04-17 16:48:30.816791'),
(3, 'owner', 'owner', 'owner', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '1111', '2025-04-08 00:00:00.000000', '2025-04-09 15:52:24.686196'),
(4, 'mananger', 'manager', 'manager', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '2222', '2025-04-08 00:00:00.000000', '2025-04-09 15:52:24.686196'),
(5, 'chef', 'chef', 'chef', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '3333', '2025-04-08 00:00:00.000000', '2025-04-09 15:52:24.686196'),
(6, 'cashier', 'cashier', 'cashier', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '4444', '2025-04-08 00:00:00.000000', '2025-04-09 15:52:24.686196'),
(7, 'waiter', 'waiter', 'waiter', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '5555', '2025-04-08 00:00:00.000000', '2025-04-18 15:58:38.000000'),
(17, 'adminusercreated', 'adminusercreated', 'adminusercreated', '<EMAIL>', 'https://testimage.png', '$2a$10$CU8H0OE6/Jfud.x/6ADaY.J2x9FrEVlH/OqWhjB5TXjcpTr6oGK9q', 1, '8888', '2025-04-18 10:50:37.121507', '2025-04-18 10:58:29.000000'),
(18, 'testh2c1', 'Test', 'H2C', '<EMAIL>', 'https://vmi1015553.contaboserver.net:6061/uploads/user.png', '$2a$10$P.8MSF2HJrTEpP7mPCP25Od64lqfRFHOUwZswHt8/MaC.ewIqrZQe', 1, '0000', '2025-04-21 17:02:59.510769', '2025-04-21 17:02:59.510769'),
(19, 'test2', 'Test2', 'test2', '<EMAIL>', 'https://vmi1015553.contaboserver.net:6061/uploads/user.png', '$2a$10$4zYl1fH/80/0JnKuaizNL.eYvbAXmd2YtIo3ggt0cYwcmibt84Fv2', 1, '0000', '2025-04-21 17:05:04.669309', '2025-04-21 17:05:04.669309'),
(20, 'test3', 'test3', 'test3', '<EMAIL>', 'https://vmi1015553.contaboserver.net:6061/uploads/1ee819a90ad4595ec10d3a7ff93a1086de.jpeg', '$2a$10$PphbfaoBm.fq3MFC/0cMOuDLy7ozLmr.2uw3uqeJRQAwaovhfgjsC', 1, '0000', '2025-04-21 17:06:07.050393', '2025-04-21 17:06:07.050393'),
(21, 'test5', 'test5', 'test5', '<EMAIL>', 'http://localhost:6061/uploads/c5109f3665bcca10246df7e92978e992fa.jpeg', '$2a$10$ZbhYom3q1cUGdmSWtCZP5uBpz7lkHdjmwPrEYWSO8VwKUfrkFGxxe', 1, '0000', '2025-04-21 17:25:13.332543', '2025-04-21 17:25:13.332543'),
(22, 'test6', 'test6', 'test6', '<EMAIL>', 'http://localhost:6061/uploads/1ef98a8dbfb8af2ee4fa65668ee6af6a.jpeg', '$2a$10$D1apX2YkE3ummJZPvKmqzuYehAweuO6CHmuYT4DhZJXcfcT5.sHXW', 1, '9894', '2025-04-21 17:30:11.447422', '2025-04-21 17:30:11.447422'),
(23, 'test7_updated', 'test7_updated', 'test7_updated', '<EMAIL>', 'http://localhost:6061/uploads/9e10a945908430820e3a9ada102556517c.jpeg', '$2a$10$KLX/KtNRXT/wK7ikdERbquUBjJ47NB/rle2Ovb2TFgZMqdcYRgzmK', 1, '7777', '2025-04-21 17:31:12.973870', '2025-04-21 17:39:40.000000'),
(24, 'TEST_12', 'TEST_12', 'TEST_12', '<EMAIL>', 'http://localhost:6061/uploads/9e6c22f108d83f897928a9fa27e1710654.jpeg', '$2a$10$53oYGIGjgxEMIyzOWTb.He3Sog4AdxPjFQyAFcykNWA7sMxSKlD5i', 1, '7323', '2025-04-21 17:43:29.665377', '2025-04-21 17:43:29.665377'),
(25, 'TEST_13', 'TEST_13', 'TEST_13', '<EMAIL>', 'http://localhost:6061/uploads/64a681142ff01179e5b86bb82d6cb3b8.jpeg', '$2a$10$viSyo12C54NSVC5XnzxjFOo0cxgccNzn9FhMuQEKnJ5fiavLn7Q1q', 1, '5389', '2025-04-21 17:45:09.919476', '2025-04-21 17:45:09.919476'),
(26, 'admin2', 'admin2', 'admin2', '<EMAIL>', NULL, '$2a$10$ElSBbn0CI.oRTknzQUWwBuIhjEGKxY3xEzACqn0UhdoICXzm.bdX6', 1, '0000', '2025-04-08 00:00:00.000000', '2025-04-17 16:48:30.816791');

-- --------------------------------------------------------

--
-- Table structure for table `user_authority`
--

CREATE TABLE IF NOT EXISTS `user_authority` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `authority_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_dc0d85509f915aa314bbfd696b7` (`user_id`),
  KEY `FK_332d35dd3c8f78a684e70d7cca0` (`authority_name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=22 ;

--
-- Dumping data for table `user_authority`
--

INSERT INTO `user_authority` (`id`, `user_id`, `authority_name`) VALUES
(1, 1, 'ROLE_ADMIN'),
(3, 3, 'ROLE_OWNER'),
(4, 4, 'ROLE_MANAGER'),
(5, 5, 'ROLE_STAFF'),
(6, 6, 'ROLE_STAFF'),
(7, 7, 'ROLE_STAFF'),
(12, 17, 'ROLE_STAFF'),
(13, 18, 'ROLE_STAFF'),
(14, 19, 'ROLE_STAFF'),
(15, 20, 'ROLE_STAFF'),
(16, 21, 'ROLE_STAFF'),
(17, 22, 'ROLE_OWNER'),
(18, 23, 'ROLE_STAFF'),
(19, 24, 'ROLE_STAFF'),
(20, 25, 'ROLE_STAFF'),
(21, 26, 'ROLE_ADMIN');

-- --------------------------------------------------------

--
-- Table structure for table `user_branch_assignment`
--

CREATE TABLE IF NOT EXISTS `user_branch_assignment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_a63512b49ebd1df762f07ba730f` (`user_id`),
  KEY `FK_2c6dd498a54d18105258e2a5aab` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=24 ;

--
-- Dumping data for table `user_branch_assignment`
--

INSERT INTO `user_branch_assignment` (`id`, `assigned_at`, `user_id`, `branch_id`) VALUES
(1, '2025-04-09 14:13:04', 1, 1),
(3, '2025-04-17 15:53:09', 3, 1),
(4, '2025-04-17 15:53:09', 4, 1),
(5, '2025-04-17 15:53:26', 5, 1),
(6, '2025-04-17 15:53:26', 6, 1),
(7, '2025-04-17 15:53:32', 7, 1),
(13, '2025-04-18 09:50:37', 17, 2),
(14, '2025-04-21 16:02:59', 18, 1),
(15, '2025-04-21 16:05:04', 19, 1),
(16, '2025-04-21 16:06:07', 20, 1),
(17, '2025-04-21 16:25:13', 21, 1),
(18, '2025-04-21 16:30:11', 22, 1),
(19, '2025-04-21 16:31:12', 23, 1),
(20, '2025-04-21 16:43:29', 24, 1),
(21, '2025-04-21 16:45:09', 25, 2),
(22, '2025-04-22 14:27:47', 26, 2),
(23, '2025-04-23 10:25:13', 1, 2);

-- --------------------------------------------------------

--
-- Table structure for table `variation`
--

CREATE TABLE IF NOT EXISTS `variation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT 'variation.png',
  `availability_status` tinyint(4) NOT NULL DEFAULT '1',
  `is_inventory_tracked` tinyint(4) NOT NULL DEFAULT '0',
  `option_id` int(11) DEFAULT NULL,
  `unit_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_c625edd870a3d28448c1bf79837` (`option_id`),
  KEY `FK_cd1089d185dd0fef64539d309f1` (`unit_id`),
  KEY `FK_66cfc0479437ce001c33863130a` (`branch_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=44 ;

--
-- Dumping data for table `variation`
--

INSERT INTO `variation` (`id`, `name`, `description`, `image_url`, `availability_status`, `is_inventory_tracked`, `option_id`, `unit_id`, `branch_id`) VALUES
(24, 'Beef', 'Beef', 'https://vmi1015553.contaboserver.net:6061/uploads/5ab9f151c7a3a255107c226502f6808d1.png', 1, 0, 8, 8, 1),
(25, 'Turkey', 'Turkey', 'https://vmi1015553.contaboserver.net:6061/uploads/8470e0716af01016fdaf27ecd80bf8ed4.png', 1, 0, 8, 8, 1),
(26, 'Pork', 'Pork', 'https://vmi1015553.contaboserver.net:6061/uploads/557506a263c8b1bcc826ef51097c52d82.png', 1, 0, 8, 8, 1),
(27, 'Small', 'Small', 'https://vmi1015553.contaboserver.net:6061/uploads/716684b12bd1010a10feb729f62cc5a15f9.png', 1, 0, 10, 13, 1),
(28, 'Medium', 'Medium', 'https://vmi1015553.contaboserver.net:6061/uploads/3a35991078f0c130d622449eef7ce47d2.png', 1, 0, 10, 13, 1),
(29, 'Large', 'Large', 'https://vmi1015553.contaboserver.net:6061/uploads/15b2ca322e10e3b499270719062b0a983.png', 1, 0, 10, 13, 1),
(30, 'Gouda', 'Gouda', 'https://vmi1015553.contaboserver.net:6061/uploads/67fe102109650e5e769e5f9433fa10bf689.png', 1, 0, 11, 8, 1),
(31, 'Cheddar', 'Cheddar', 'https://vmi1015553.contaboserver.net:6061/uploads/3948f717a6a7cb5a9a35d39c2c72efe8.png', 1, 0, 11, 8, 1),
(32, 'Edam', 'Edam', 'http://localhost:6061/uploads/variation.png', 1, 0, 11, 8, 1),
(33, 'No sugar', 'No sugar', 'https://vmi1015553.contaboserver.net:6061/uploads/6fea13109742bcb08927b5376107a6c774.png', 1, 0, 12, 13, 1),
(34, 'Regular', 'Regular', 'https://vmi1015553.contaboserver.net:6061/uploads/95dc6326d431014e917290c98d10feaf4d.png', 1, 0, 12, 13, 1),
(35, 'Extra Sweet', 'Extra Sweet', 'https://vmi1015553.contaboserver.net:6061/uploads/2e82454d1d910c10b989fd6b2dea54697b.png', 1, 0, 12, 13, 1),
(36, 'Mustard', 'Mustard', 'https://vmi1015553.contaboserver.net:6061/uploads/8b91f971f8a1c4ec52392ca9c9ea5f36.png', 1, 0, 9, 10, 1),
(37, 'Almond', 'Almond', 'https://vmi1015553.contaboserver.net:6061/uploads/856b7f18f4f9b4bede02dac66c5f989c.png', 1, 0, 13, 10, 1),
(38, 'Soy', 'Soy', 'https://vmi1015553.contaboserver.net:6061/uploads/142febdf2cb1cdfc3932c9c6a9727e1c.png', 1, 0, 13, 10, 1),
(40, 'Oat', 'Oat', 'https://vmi1015553.contaboserver.net:6061/uploads/8c188e3904137f5270a7f33f762f10334.png', 1, 1, 13, 10, 1),
(42, 'Oat1', 'Oat1', 'https://vmi1015553.contaboserver.net:6061/uploads/8c188e3904137f5270a7f33f762f10334.png', 1, 0, 13, NULL, 2),
(43, 'Oat2', 'Oat2', 'http://localhost:6061/uploads/variation.png', 1, 0, 13, NULL, 2);

--
-- Constraints for dumped tables
--

--
-- Constraints for table `addons`
--
ALTER TABLE `addons`
  ADD CONSTRAINT `FK_815323a8f8b87cf2d3c5e57345e` FOREIGN KEY (`addon_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_ee37825d7df7f4ddd35927779dd` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD CONSTRAINT `FK_355ceab7b156f4ac359b19a9380` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_80e2e8cc5eec1cda45594b634c0` FOREIGN KEY (`performed_by`) REFERENCES `user` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `branch`
--
ALTER TABLE `branch`
  ADD CONSTRAINT `FK_1fc25d651485556af43654bb110` FOREIGN KEY (`store_id`) REFERENCES `store` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `category`
--
ALTER TABLE `category`
  ADD CONSTRAINT `FK_16dcaf904b91226a3ba6950b5f0` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `coupon`
--
ALTER TABLE `coupon`
  ADD CONSTRAINT `FK_ef87e85f675967c83dfe80a5139` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `discount`
--
ALTER TABLE `discount`
  ADD CONSTRAINT `FK_207ea3c6100ab95ddd43b90ff88` FOREIGN KEY (`store_id`) REFERENCES `store` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `inventory`
--
ALTER TABLE `inventory`
  ADD CONSTRAINT `FK_33f7c9dc1f52390aa417c4f77b4` FOREIGN KEY (`variation_id`) REFERENCES `variation` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_5e4d38ade6f246f20f468a7ad4b` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_732fdb1f76432d65d2c136340dc` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `kiosk`
--
ALTER TABLE `kiosk`
  ADD CONSTRAINT `FK_e2fd29b7aabd8cc510dadc0df3e` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `loyalty`
--
ALTER TABLE `loyalty`
  ADD CONSTRAINT `FK_454c65be2b44f552b6766f2dcf1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_a5d772a026e06617161841c5664` FOREIGN KEY (`store_id`) REFERENCES `store` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `notification`
--
ALTER TABLE `notification`
  ADD CONSTRAINT `FK_3ea5cd8a1de9cbf90c86dd0582c` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `option`
--
ALTER TABLE `option`
  ADD CONSTRAINT `FK_f9bd0c5005ece46cbb3cd947462` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `order`
--
ALTER TABLE `order`
  ADD CONSTRAINT `FK_a2b1fd48f82280f14b983066149` FOREIGN KEY (`kiosk_id`) REFERENCES `kiosk` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_b9ed44eb87f5c9df365aa5adf90` FOREIGN KEY (`pos_id`) REFERENCES `pos` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_baced9282892a60354aaa789fb4` FOREIGN KEY (`coupon_id`) REFERENCES `coupon` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_bffc1bebf6db72c49d80df64b24` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_cd7812c96209c5bdd48a6b858b0` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `order_category_summary`
--
ALTER TABLE `order_category_summary`
  ADD CONSTRAINT `FK_e35a85c356c196e7bc47a9533ee` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_e3af1acf28cf3c28f7d56b6b611` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `order_detail`
--
ALTER TABLE `order_detail`
  ADD CONSTRAINT `FK_264cb1d910f77be80066a7591d2` FOREIGN KEY (`variation_id`) REFERENCES `variation` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_985d5f728e1eebe4a3eabc43aac` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_a6ac5c99b8c02bd4ee53d3785be` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `order_summary`
--
ALTER TABLE `order_summary`
  ADD CONSTRAINT `FK_9fe12bd3f473edb844707d24fa6` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `payment`
--
ALTER TABLE `payment`
  ADD CONSTRAINT `FK_365af7f69f9142427cf30395b00` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_method` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_f5221735ace059250daac9d9803` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `pos`
--
ALTER TABLE `pos`
  ADD CONSTRAINT `FK_287bdf629337d91b27b74f6e148` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `pos_session`
--
ALTER TABLE `pos_session`
  ADD CONSTRAINT `FK_dcb1a6a83f4aa4a9f539ffccce1` FOREIGN KEY (`pos_id`) REFERENCES `pos` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `product`
--
ALTER TABLE `product`
  ADD CONSTRAINT `FK_0dce9bc93c2d2c399982d04bef1` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_47ec9f981fac28851de1d6bd8db` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_b15422982adca3bf53adfb535de` FOREIGN KEY (`unit_id`) REFERENCES `unit` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `product_discounts`
--
ALTER TABLE `product_discounts`
  ADD CONSTRAINT `FK_4d3f74396271c6bee81a1a547a0` FOREIGN KEY (`discount_id`) REFERENCES `discount` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_68e668b0341f0276ebcc2a91506` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `product_option`
--
ALTER TABLE `product_option`
  ADD CONSTRAINT `FK_9f53e0e9868b4d64b048bff8701` FOREIGN KEY (`option_id`) REFERENCES `option` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_e634fca34f6b594b87fdbee95f6` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `product_taxes`
--
ALTER TABLE `product_taxes`
  ADD CONSTRAINT `FK_1d39e319e41b1edce44933cfe75` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_db8c22b3e1e26adae58c92dc64e` FOREIGN KEY (`tax_id`) REFERENCES `tax` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `product_variations`
--
ALTER TABLE `product_variations`
  ADD CONSTRAINT `FK_318ea43851e4dcc507601cc9f08` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_85acd982176dfe61b299cd8da68` FOREIGN KEY (`variation_id`) REFERENCES `variation` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `purchase`
--
ALTER TABLE `purchase`
  ADD CONSTRAINT `FK_8d9a856657c46725d085c73e4fc` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_ac1a50d78592b601510bd8a0e53` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `purchase_details`
--
ALTER TABLE `purchase_details`
  ADD CONSTRAINT `FK_7769941f7424a0030e1f6c7b7d3` FOREIGN KEY (`purchase_id`) REFERENCES `purchase` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_802cd8f2a3c2e09932fc1bfad88` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_972c5d1577048dfa45128ed03d4` FOREIGN KEY (`variation_id`) REFERENCES `variation` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `purchase_summary`
--
ALTER TABLE `purchase_summary`
  ADD CONSTRAINT `FK_784a4323a220278d58eeeae3ae1` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `tax_category`
--
ALTER TABLE `tax_category`
  ADD CONSTRAINT `FK_4bf319f5d3986cbb0584de97073` FOREIGN KEY (`tax_id`) REFERENCES `tax` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_7fdc0186b0a90d5b2172e53d6ca` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `tax_product`
--
ALTER TABLE `tax_product`
  ADD CONSTRAINT `FK_cc9411ac13a4f1bd0f70f960c34` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_e86258e270cc3ac2d1378d5e4a5` FOREIGN KEY (`tax_id`) REFERENCES `tax` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `tax_store`
--
ALTER TABLE `tax_store`
  ADD CONSTRAINT `FK_e096f75ac0aa71ab309034ab38a` FOREIGN KEY (`tax_id`) REFERENCES `tax` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_e74095e2d518dab6aa7a265b26a` FOREIGN KEY (`store_id`) REFERENCES `store` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `translation`
--
ALTER TABLE `translation`
  ADD CONSTRAINT `FK_1c14f78e719ce89380c7680841c` FOREIGN KEY (`option_id`) REFERENCES `option` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_39403d84a9575d02f0c4bcdef32` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_3b4a58c7173cadb208b333e50c1` FOREIGN KEY (`variation_id`) REFERENCES `variation` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_eb51c35ae5b9d90a24939049260` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_f23f2870f14fc46735f85869bee` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `user_authority`
--
ALTER TABLE `user_authority`
  ADD CONSTRAINT `FK_332d35dd3c8f78a684e70d7cca0` FOREIGN KEY (`authority_name`) REFERENCES `authority` (`name`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_dc0d85509f915aa314bbfd696b7` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `user_branch_assignment`
--
ALTER TABLE `user_branch_assignment`
  ADD CONSTRAINT `FK_2c6dd498a54d18105258e2a5aab` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_a63512b49ebd1df762f07ba730f` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Constraints for table `variation`
--
ALTER TABLE `variation`
  ADD CONSTRAINT `FK_66cfc0479437ce001c33863130a` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_c625edd870a3d28448c1bf79837` FOREIGN KEY (`option_id`) REFERENCES `option` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `FK_cd1089d185dd0fef64539d309f1` FOREIGN KEY (`unit_id`) REFERENCES `unit` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
