import { Test, TestingModule } from '@nestjs/testing';
import { ProviderUserServiceController } from './provider-user-service.controller';
import { ProviderUserServiceService } from './provider-user-service.service';
import { CreateProviderUserServiceDto } from './dto/create-provider-user-service.dto';
import { UpdateProviderUserServiceDto } from './dto/update-provider-user-service.dto';
import { ProviderUserService } from './entities/provider-user-service.entity';
import { Availability } from '../../common/enums/availability.enum';
import { UserRole } from '../../common/enums/user-role.enum';

describe('ProviderUserServiceController', () => {
  let controller: ProviderUserServiceController;
  let service: ProviderUserServiceService;

  const mockUser = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: UserRole.PROVIDER,
  };

  const mockService = {
    id: '1',
    name: 'Web Development',
    icon: 'web-icon',
    imageUrl: 'https://example.com/web.jpg',
  };

  const mockProviderUserService: ProviderUserService = {
    id: '1',
    user: mockUser as any,
    service: mockService as any,
    contacts: [],
    bio: 'Experienced web developer',
    rating: 4.5,
    reviewCount: 10,
    isAvailable: true,
    availability: Availability.ANYTIME,
    comments: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockProviderUserServiceService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findAllAvailable: jest.fn(),
    findOne: jest.fn(),
    findByUser: jest.fn(),
    findByService: jest.fn(),
    findByAvailability: jest.fn(),
    findTopRated: jest.fn(),
    update: jest.fn(),
    updateRating: jest.fn(),
    toggleAvailability: jest.fn(),
    addContact: jest.fn(),
    removeContact: jest.fn(),
    remove: jest.fn(),
    getProviderStats: jest.fn(),
    searchProviders: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProviderUserServiceController],
      providers: [
        {
          provide: ProviderUserServiceService,
          useValue: mockProviderUserServiceService,
        },
      ],
    }).compile();

    controller = module.get<ProviderUserServiceController>(ProviderUserServiceController);
    service = module.get<ProviderUserServiceService>(ProviderUserServiceService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a provider user service', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
        bio: 'Experienced web developer',
        isAvailable: true,
        availability: Availability.ANYTIME,
      };

      mockProviderUserServiceService.create.mockResolvedValue(mockProviderUserService);

      const result = await controller.create(createDto);

      expect(service.create).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(mockProviderUserService);
    });
  });

  describe('findAll', () => {
    it('should return all provider user services', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findAll.mockResolvedValue(providerServices);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(providerServices);
    });

    it('should return available provider services when available=true', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findAllAvailable.mockResolvedValue(providerServices);

      const result = await controller.findAll(true);

      expect(service.findAllAvailable).toHaveBeenCalled();
      expect(result).toEqual(providerServices);
    });

    it('should search providers when search term provided', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.searchProviders.mockResolvedValue(providerServices);

      const result = await controller.findAll(undefined, undefined, undefined, undefined, 'web');

      expect(service.searchProviders).toHaveBeenCalledWith('web');
      expect(result).toEqual(providerServices);
    });

    it('should return top rated providers when topRated=true', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findTopRated.mockResolvedValue(providerServices);

      const result = await controller.findAll(undefined, undefined, undefined, undefined, undefined, true, 5);

      expect(service.findTopRated).toHaveBeenCalledWith(5);
      expect(result).toEqual(providerServices);
    });

    it('should return providers by user when userId provided', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findByUser.mockResolvedValue(providerServices);

      const result = await controller.findAll(undefined, '1');

      expect(service.findByUser).toHaveBeenCalledWith('1');
      expect(result).toEqual(providerServices);
    });

    it('should return providers by service when serviceId provided', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findByService.mockResolvedValue(providerServices);

      const result = await controller.findAll(undefined, undefined, '1');

      expect(service.findByService).toHaveBeenCalledWith('1');
      expect(result).toEqual(providerServices);
    });

    it('should return providers by availability when availability provided', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceService.findByAvailability.mockResolvedValue(providerServices);

      const result = await controller.findAll(undefined, undefined, undefined, Availability.MORNING);

      expect(service.findByAvailability).toHaveBeenCalledWith(Availability.MORNING);
      expect(result).toEqual(providerServices);
    });
  });

  describe('findOne', () => {
    it('should return a provider user service by id', async () => {
      mockProviderUserServiceService.findOne.mockResolvedValue(mockProviderUserService);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockProviderUserService);
    });
  });

  describe('update', () => {
    it('should update a provider user service', async () => {
      const updateDto: UpdateProviderUserServiceDto = {
        bio: 'Updated bio',
        isAvailable: false,
      };
      const updatedProviderService = { ...mockProviderUserService, ...updateDto };

      mockProviderUserServiceService.update.mockResolvedValue(updatedProviderService);

      const result = await controller.update('1', updateDto);

      expect(service.update).toHaveBeenCalledWith('1', updateDto);
      expect(result).toEqual(updatedProviderService);
    });
  });

  describe('updateRating', () => {
    it('should update provider rating', async () => {
      const updatedProviderService = { ...mockProviderUserService, rating: 4.8, reviewCount: 11 };
      mockProviderUserServiceService.updateRating.mockResolvedValue(updatedProviderService);

      const result = await controller.updateRating('1', 5);

      expect(service.updateRating).toHaveBeenCalledWith('1', 5);
      expect(result).toEqual(updatedProviderService);
    });
  });

  describe('toggleAvailability', () => {
    it('should toggle provider availability', async () => {
      const toggledProviderService = { ...mockProviderUserService, isAvailable: false };
      mockProviderUserServiceService.toggleAvailability.mockResolvedValue(toggledProviderService);

      const result = await controller.toggleAvailability('1');

      expect(service.toggleAvailability).toHaveBeenCalledWith('1');
      expect(result).toEqual(toggledProviderService);
    });
  });

  describe('addContact', () => {
    it('should add contact to provider', async () => {
      mockProviderUserServiceService.addContact.mockResolvedValue(mockProviderUserService);

      const result = await controller.addContact('1', 'contact1');

      expect(service.addContact).toHaveBeenCalledWith('1', 'contact1');
      expect(result).toEqual(mockProviderUserService);
    });
  });

  describe('removeContact', () => {
    it('should remove contact from provider', async () => {
      mockProviderUserServiceService.removeContact.mockResolvedValue(mockProviderUserService);

      const result = await controller.removeContact('1', 'contact1');

      expect(service.removeContact).toHaveBeenCalledWith('1', 'contact1');
      expect(result).toEqual(mockProviderUserService);
    });
  });

  describe('remove', () => {
    it('should remove a provider user service', async () => {
      mockProviderUserServiceService.remove.mockResolvedValue(undefined);

      await controller.remove('1');

      expect(service.remove).toHaveBeenCalledWith('1');
    });
  });

  describe('getStats', () => {
    it('should return provider statistics', async () => {
      const stats = {
        total: 10,
        available: 8,
        unavailable: 2,
        averageRating: 4.2,
        totalReviews: 50,
        byAvailability: {
          [Availability.ANYTIME]: 5,
          [Availability.MORNING]: 2,
          [Availability.AFTERNOON]: 1,
          [Availability.EVENING]: 1,
          [Availability.NIGHT]: 1,
          [Availability.WEEKDAYS]: 0,
          [Availability.WEEKENDS]: 0,
        },
      };
      mockProviderUserServiceService.getProviderStats.mockResolvedValue(stats);

      const result = await controller.getStats('user1');

      expect(service.getProviderStats).toHaveBeenCalledWith('user1');
      expect(result).toEqual(stats);
    });
  });
});
