import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { AuthorityService } from './authority.service';
import { CreateAuthorityDto } from './dto/create-authority.dto';
import { UpdateAuthorityDto } from './dto/update-authority.dto';

@Controller('authority')
export class AuthorityController {
  constructor(private readonly authorityService: AuthorityService) {}

  @Post()
  async create(@Body() createAuthorityDto: CreateAuthorityDto) {
    return await this.authorityService.create(createAuthorityDto);
  }

  @Get()
  async findAll() {
    return await this.authorityService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.authorityService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAuthorityDto: UpdateAuthorityDto,
  ) {
    return await this.authorityService.update(id, updateAuthorityDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return await this.authorityService.remove(id);
  }
}
