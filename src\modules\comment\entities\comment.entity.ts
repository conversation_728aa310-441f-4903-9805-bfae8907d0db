import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { ProviderUserService } from 'src/modules/provider-user-service/entities/provider-user-service.entity';

@Entity('comment')
export class Comment {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isEdited: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, { nullable: true })
  author: User;

  @ManyToOne(() => ProviderUserService, { nullable: true })
  providerUserService: ProviderUserService;
}
