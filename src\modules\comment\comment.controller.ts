import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { CommentService } from './comment.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { Comment } from './entities/comment.entity';

@Controller('comments')
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @Post()
  async create(@Body() createCommentDto: CreateCommentDto): Promise<Comment> {
    return await this.commentService.create(createCommentDto);
  }

  @Get()
  async findAll(
    @Query('active') active?: boolean,
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('authorId') authorId?: string,
  ): Promise<Comment[]> {
    if (entityType && entityId) {
      return await this.commentService.findByEntity(entityType, entityId);
    }

    if (authorId) {
      return await this.commentService.findByAuthor(authorId);
    }

    if (active !== undefined) {
      return active
        ? await this.commentService.findAllActive()
        : await this.commentService.findAll();
    }

    return await this.commentService.findAll();
  }

  @Get('stats')
  async getStats(
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
  ) {
    return await this.commentService.getCommentStats(entityType, entityId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Comment> {
    return await this.commentService.findOne(id);
  }

  @Get(':id/replies')
  async findReplies(@Param('id') id: string): Promise<Comment[]> {
    return await this.commentService.findReplies(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCommentDto: UpdateCommentDto,
  ): Promise<Comment> {
    return await this.commentService.update(id, updateCommentDto);
  }

  @Patch(':id/user')
  async updateByUser(
    @Param('id') id: string,
    @Body() updateCommentDto: UpdateCommentDto,
    @Req() req: any,
  ): Promise<Comment> {
    const userId = req.user?.userId || req.user?.id;
    return await this.commentService.updateByUser(id, updateCommentDto, userId);
  }

  @Patch(':id/toggle-status')
  async toggleActiveStatus(@Param('id') id: string): Promise<Comment> {
    return await this.commentService.toggleActiveStatus(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return await this.commentService.remove(id);
  }

  @Delete(':id/soft')
  async softDelete(@Param('id') id: string): Promise<Comment> {
    return await this.commentService.softDelete(id);
  }

  @Delete(':id/user')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteByUser(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<void> {
    const userId = req.user?.userId || req.user?.id;
    return await this.commentService.deleteByUser(id, userId);
  }
}
