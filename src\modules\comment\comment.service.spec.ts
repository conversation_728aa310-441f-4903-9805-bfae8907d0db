import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommentService } from './comment.service';
import { Comment } from './entities/comment.entity';
import { User } from '../user/entities/user.entity';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { NotFoundException, ForbiddenException } from '@nestjs/common';

describe('CommentService', () => {
  let service: CommentService;
  let commentRepository: Repository<Comment>;
  let userRepository: Repository<User>;

  const mockUser = {
    id: 'user1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
  };

  const mockComment: Comment = {
    id: '1',
    content: 'This is a test comment',
    entityType: 'product',
    entityId: '123',
    isActive: true,
    isEdited: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    author: mockUser as User,
    parentComment: null,
    replies: [],
  };

  const mockCommentRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getCount: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommentService,
        {
          provide: getRepositoryToken(Comment),
          useValue: mockCommentRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<CommentService>(CommentService);
    commentRepository = module.get<Repository<Comment>>(getRepositoryToken(Comment));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a comment successfully', async () => {
      const createCommentDto: CreateCommentDto = {
        content: 'This is a test comment',
        entityType: 'product',
        entityId: '123',
        authorId: 'user1',
      };

      mockCommentRepository.create.mockReturnValue(mockComment);
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockCommentRepository.save.mockResolvedValue(mockComment);

      const result = await service.create(createCommentDto);

      expect(commentRepository.create).toHaveBeenCalledWith(createCommentDto);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user1' },
      });
      expect(commentRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockComment);
    });

    it('should create a comment with parent comment', async () => {
      const createCommentDto: CreateCommentDto = {
        content: 'This is a reply',
        parentCommentId: '1',
      };

      const parentComment = { ...mockComment };
      mockCommentRepository.create.mockReturnValue(mockComment);
      mockCommentRepository.findOne.mockResolvedValue(parentComment);
      mockCommentRepository.save.mockResolvedValue(mockComment);

      const result = await service.create(createCommentDto);

      expect(commentRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual(mockComment);
    });
  });

  describe('findAll', () => {
    it('should return all comments', async () => {
      const comments = [mockComment];
      mockCommentRepository.find.mockResolvedValue(comments);

      const result = await service.findAll();

      expect(commentRepository.find).toHaveBeenCalledWith({
        relations: ['author', 'parentComment'],
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(comments);
    });
  });

  describe('findOne', () => {
    it('should return a comment by id', async () => {
      mockCommentRepository.findOne.mockResolvedValue(mockComment);

      const result = await service.findOne('1');

      expect(commentRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['author', 'parentComment'],
      });
      expect(result).toEqual(mockComment);
    });

    it('should throw NotFoundException if comment not found', async () => {
      mockCommentRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByEntity', () => {
    it('should return comments for a specific entity', async () => {
      const comments = [mockComment];
      mockCommentRepository.find.mockResolvedValue(comments);

      const result = await service.findByEntity('product', '123');

      expect(commentRepository.find).toHaveBeenCalledWith({
        where: { 
          entityType: 'product', 
          entityId: '123',
          isActive: true,
          parentComment: null
        },
        relations: ['author', 'replies', 'replies.author'],
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(comments);
    });
  });

  describe('update', () => {
    it('should update a comment successfully', async () => {
      const updateCommentDto: UpdateCommentDto = {
        content: 'Updated comment',
      };
      const updatedComment = { ...mockComment, content: 'Updated comment', isEdited: true };

      mockCommentRepository.findOne.mockResolvedValue(mockComment);
      mockCommentRepository.save.mockResolvedValue(updatedComment);

      const result = await service.update('1', updateCommentDto);

      expect(result.isEdited).toBe(true);
    });

    it('should throw NotFoundException if comment not found', async () => {
      const updateCommentDto: UpdateCommentDto = {
        content: 'Updated comment',
      };

      mockCommentRepository.findOne.mockResolvedValue(null);

      await expect(service.update('1', updateCommentDto)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('canUserModifyComment', () => {
    it('should return true if user is the author', async () => {
      mockCommentRepository.findOne.mockResolvedValue(mockComment);

      const result = await service.canUserModifyComment('1', 'user1');

      expect(result).toBe(true);
    });

    it('should return false if user is not the author', async () => {
      mockCommentRepository.findOne.mockResolvedValue(mockComment);

      const result = await service.canUserModifyComment('1', 'user2');

      expect(result).toBe(false);
    });

    it('should return false if comment not found', async () => {
      mockCommentRepository.findOne.mockResolvedValue(null);

      const result = await service.canUserModifyComment('1', 'user1');

      expect(result).toBe(false);
    });
  });

  describe('updateByUser', () => {
    it('should update comment if user is the author', async () => {
      const updateCommentDto: UpdateCommentDto = {
        content: 'Updated by user',
      };

      jest.spyOn(service, 'canUserModifyComment').mockResolvedValue(true);
      jest.spyOn(service, 'update').mockResolvedValue(mockComment);

      const result = await service.updateByUser('1', updateCommentDto, 'user1');

      expect(service.canUserModifyComment).toHaveBeenCalledWith('1', 'user1');
      expect(service.update).toHaveBeenCalledWith('1', updateCommentDto);
      expect(result).toEqual(mockComment);
    });

    it('should throw ForbiddenException if user is not the author', async () => {
      const updateCommentDto: UpdateCommentDto = {
        content: 'Updated by user',
      };

      jest.spyOn(service, 'canUserModifyComment').mockResolvedValue(false);

      await expect(
        service.updateByUser('1', updateCommentDto, 'user2')
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getCommentStats', () => {
    it('should return comment statistics', async () => {
      mockCommentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockQueryBuilder.getCount.mockResolvedValueOnce(10).mockResolvedValueOnce(8);

      const result = await service.getCommentStats('product', '123');

      expect(result).toEqual({
        total: 10,
        active: 8,
        inactive: 2,
      });
    });
  });
});
