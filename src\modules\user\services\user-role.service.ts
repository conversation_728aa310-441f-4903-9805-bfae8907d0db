import { Injectable, ForbiddenException } from '@nestjs/common';
import { UserRole } from '../../../common/enums/user-role.enum';
import { User } from '../entities/user.entity';
import { RoleUtil } from '../../../common/utils/role.util';

@Injectable()
export class UserRoleService {
  
  /**
   * Check if user can perform admin actions
   */
  canPerformAdminActions(user: User): boolean {
    return RoleUtil.isAdmin(user);
  }

  /**
   * Check if user can manage services
   */
  canManageServices(user: User): boolean {
    return RoleUtil.canManageServices(user);
  }

  /**
   * Check if user can book services
   */
  canBookServices(user: User): boolean {
    return RoleUtil.canBookServices(user);
  }

  /**
   * Check if user can access admin panel
   */
  canAccessAdminPanel(user: User): boolean {
    return RoleUtil.canAccessAdminFeatures(user);
  }

  /**
   * Check if user can manage other users
   */
  canManageUsers(user: User): boolean {
    return RoleUtil.isAdmin(user);
  }

  /**
   * Check if user can update their own profile
   */
  canUpdateOwnProfile(user: User, targetUserId: string): boolean {
    return user.id === targetUserId || RoleUtil.isAdmin(user);
  }

  /**
   * Check if user can update another user's role
   */
  canUpdateUserRole(user: User, targetUser: User): boolean {
    // Only admins can change roles
    if (!RoleUtil.isAdmin(user)) {
      return false;
    }

    // Admins cannot change their own role (prevent lockout)
    if (user.id === targetUser.id) {
      return false;
    }

    return true;
  }

  /**
   * Get allowed roles for user creation based on current user's role
   */
  getAllowedRolesForCreation(user: User): UserRole[] {
    if (RoleUtil.isAdmin(user)) {
      return [UserRole.CLIENT, UserRole.PROVIDER]; // Admins can create clients and providers
    }
    
    return [UserRole.CLIENT]; // Others can only create clients (self-registration)
  }

  /**
   * Validate role assignment
   */
  validateRoleAssignment(currentUser: User, targetRole: UserRole): void {
    const allowedRoles = this.getAllowedRolesForCreation(currentUser);
    
    if (!allowedRoles.includes(targetRole)) {
      throw new ForbiddenException(
        `You are not authorized to assign the role: ${RoleUtil.getRoleDisplayName(targetRole)}`
      );
    }
  }

  /**
   * Get user permissions based on role
   */
  getUserPermissions(user: User): {
    canAccessAdmin: boolean;
    canManageServices: boolean;
    canBookServices: boolean;
    canManageUsers: boolean;
    canViewReports: boolean;
  } {
    return {
      canAccessAdmin: this.canAccessAdminPanel(user),
      canManageServices: this.canManageServices(user),
      canBookServices: this.canBookServices(user),
      canManageUsers: this.canManageUsers(user),
      canViewReports: RoleUtil.isAdmin(user) || RoleUtil.isProvider(user),
    };
  }

  /**
   * Get role-specific dashboard data
   */
  getRoleDashboardConfig(user: User): {
    dashboardType: string;
    allowedMenuItems: string[];
    defaultRoute: string;
  } {
    switch (user.role) {
      case UserRole.ADMIN:
        return {
          dashboardType: 'admin',
          allowedMenuItems: ['users', 'services', 'providers', 'reports', 'settings'],
          defaultRoute: '/admin/dashboard',
        };
      
      case UserRole.PROVIDER:
        return {
          dashboardType: 'provider',
          allowedMenuItems: ['my-services', 'bookings', 'profile', 'reports'],
          defaultRoute: '/provider/dashboard',
        };
      
      case UserRole.CLIENT:
        return {
          dashboardType: 'client',
          allowedMenuItems: ['services', 'my-bookings', 'profile'],
          defaultRoute: '/client/dashboard',
        };
      
      default:
        return {
          dashboardType: 'guest',
          allowedMenuItems: ['services'],
          defaultRoute: '/',
        };
    }
  }
}
