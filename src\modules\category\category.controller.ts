import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';

@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<Category> {
    return await this.categoryService.create(createCategoryDto);
  }

  @Get()
  async findAll(
    @Query('active') active?: boolean,
    @Query('search') search?: string,
  ): Promise<Category[]> {
    if (search) {
      return await this.categoryService.searchCategories(search);
    }

    if (active !== undefined) {
      return active
        ? await this.categoryService.findAllActive()
        : await this.categoryService.findAll();
    }

    return await this.categoryService.findAll();
  }

  @Get('stats')
  async getStats() {
    return await this.categoryService.getCategoryStats();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Category> {
    return await this.categoryService.findOne(id);
  }

  @Get('name/:name')
  async findByName(@Param('name') name: string): Promise<Category> {
    return await this.categoryService.findByName(name);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    return await this.categoryService.update(id, updateCategoryDto);
  }

  @Patch(':id/toggle-status')
  async toggleActiveStatus(@Param('id') id: string): Promise<Category> {
    return await this.categoryService.toggleActiveStatus(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return await this.categoryService.remove(id);
  }
}
