import { Injectable } from '@nestjs/common';
import { CreateLanguageDto } from './dto/create-language.dto';
import { UpdateLanguageDto } from './dto/update-language.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Language } from './entities/language.entity';

@Injectable()
export class LanguageService {
  constructor(
    @InjectRepository(Language)
    private readonly languageRepository: Repository<Language>,
  ) {}

  async create(createLanguageDto: CreateLanguageDto) {
    const language = this.languageRepository.create(createLanguageDto);
    return this.languageRepository.save(language);
  }

  async findAll() {
    return this.languageRepository.find();
  }

  async findOne(id: string) {
    return this.languageRepository.findOne({ where: { id } });
  }

  async update(id: string, updateLanguageDto: UpdateLanguageDto) {
    return this.languageRepository.update(id, updateLanguageDto);
  }

  async remove(id: string) {
    return this.languageRepository.delete(id);
  }
}
