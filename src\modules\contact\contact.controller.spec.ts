import { Test, TestingModule } from '@nestjs/testing';
import { ContactController } from './contact.controller';
import { ContactService } from './contact.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { Contact } from './entities/contact.entity';

describe('ContactController', () => {
  let controller: ContactController;
  let service: ContactService;

  const mockContact: Contact = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    company: 'Acme Corp',
    position: 'Developer',
    address: '123 Main St',
    city: 'New York',
    country: 'USA',
    notes: 'Test contact',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockContactService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findAllActive: jest.fn(),
    findOne: jest.fn(),
    findByEmail: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    toggleActiveStatus: jest.fn(),
    searchContacts: jest.fn(),
    findByCompany: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContactController],
      providers: [
        {
          provide: ContactService,
          useValue: mockContactService,
        },
      ],
    }).compile();

    controller = module.get<ContactController>(ContactController);
    service = module.get<ContactService>(ContactService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a contact', async () => {
      const createContactDto: CreateContactDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
        company: 'Acme Corp',
        position: 'Developer',
        address: '123 Main St',
        city: 'New York',
        country: 'USA',
        notes: 'Test contact',
        isActive: true,
      };

      mockContactService.create.mockResolvedValue(mockContact);

      const result = await controller.create(createContactDto);

      expect(service.create).toHaveBeenCalledWith(createContactDto);
      expect(result).toEqual(mockContact);
    });
  });

  describe('findAll', () => {
    it('should return all contacts', async () => {
      const contacts = [mockContact];
      mockContactService.findAll.mockResolvedValue(contacts);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(contacts);
    });

    it('should return active contacts when active=true', async () => {
      const contacts = [mockContact];
      mockContactService.findAllActive.mockResolvedValue(contacts);

      const result = await controller.findAll(true);

      expect(service.findAllActive).toHaveBeenCalled();
      expect(result).toEqual(contacts);
    });

    it('should search contacts when search term provided', async () => {
      const contacts = [mockContact];
      mockContactService.searchContacts.mockResolvedValue(contacts);

      const result = await controller.findAll(undefined, 'John');

      expect(service.searchContacts).toHaveBeenCalledWith('John');
      expect(result).toEqual(contacts);
    });
  });

  describe('findOne', () => {
    it('should return a contact by id', async () => {
      mockContactService.findOne.mockResolvedValue(mockContact);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockContact);
    });
  });

  describe('update', () => {
    it('should update a contact', async () => {
      const updateContactDto: UpdateContactDto = {
        firstName: 'Jane',
      };
      const updatedContact = { ...mockContact, firstName: 'Jane' };

      mockContactService.update.mockResolvedValue(updatedContact);

      const result = await controller.update('1', updateContactDto);

      expect(service.update).toHaveBeenCalledWith('1', updateContactDto);
      expect(result).toEqual(updatedContact);
    });
  });

  describe('remove', () => {
    it('should remove a contact', async () => {
      mockContactService.remove.mockResolvedValue(undefined);

      await controller.remove('1');

      expect(service.remove).toHaveBeenCalledWith('1');
    });
  });
});
