import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceService } from './service.service';
import { Service } from './entities/service.entity';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { NotFoundException, ConflictException } from '@nestjs/common';

describe('ServiceService', () => {
  let service: ServiceService;
  let repository: Repository<Service>;

  const mockService: Service = {
    id: '1',
    name: 'Web Development',
    icon: 'web-icon',
    imageUrl: 'https://example.com/web-dev.jpg',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceService,
        {
          provide: getRepositoryToken(Service),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<ServiceService>(ServiceService);
    repository = module.get<Repository<Service>>(getRepositoryToken(Service));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a service successfully', async () => {
      const createServiceDto: CreateServiceDto = {
        name: 'Web Development',
        icon: 'web-icon',
        imageUrl: 'https://example.com/web-dev.jpg',
        isActive: true,
      };

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.create.mockReturnValue(mockService);
      mockRepository.save.mockResolvedValue(mockService);

      const result = await service.create(createServiceDto);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { name: createServiceDto.name },
      });
      expect(repository.create).toHaveBeenCalledWith(createServiceDto);
      expect(repository.save).toHaveBeenCalledWith(mockService);
      expect(result).toEqual(mockService);
    });

    it('should throw ConflictException if service name already exists', async () => {
      const createServiceDto: CreateServiceDto = {
        name: 'Web Development',
        icon: 'web-icon',
        imageUrl: 'https://example.com/web-dev.jpg',
      };

      mockRepository.findOne.mockResolvedValue(mockService);

      await expect(service.create(createServiceDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('findAll', () => {
    it('should return all services', async () => {
      const services = [mockService];
      mockRepository.find.mockResolvedValue(services);

      const result = await service.findAll();

      expect(repository.find).toHaveBeenCalledWith({
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(services);
    });
  });

  describe('findAllActive', () => {
    it('should return all active services', async () => {
      const services = [mockService];
      mockRepository.find.mockResolvedValue(services);

      const result = await service.findAllActive();

      expect(repository.find).toHaveBeenCalledWith({
        where: { isActive: true },
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(services);
    });
  });

  describe('findOne', () => {
    it('should return a service by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockService);

      const result = await service.findOne('1');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual(mockService);
    });

    it('should throw NotFoundException if service not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByName', () => {
    it('should return a service by name', async () => {
      mockRepository.findOne.mockResolvedValue(mockService);

      const result = await service.findByName('Web Development');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { name: 'Web Development' },
      });
      expect(result).toEqual(mockService);
    });

    it('should throw NotFoundException if service not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findByName('Web Development')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a service successfully', async () => {
      const updateServiceDto: UpdateServiceDto = {
        name: 'Mobile Development',
      };
      const updatedService = { ...mockService, name: 'Mobile Development' };

      mockRepository.findOne.mockResolvedValue(mockService);
      mockRepository.save.mockResolvedValue(updatedService);

      const result = await service.update('1', updateServiceDto);

      expect(result).toEqual(updatedService);
    });

    it('should throw NotFoundException if service not found', async () => {
      const updateServiceDto: UpdateServiceDto = {
        name: 'Mobile Development',
      };

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update('1', updateServiceDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ConflictException if new name already exists', async () => {
      const updateServiceDto: UpdateServiceDto = {
        name: 'Mobile Development',
      };
      const existingService = { ...mockService, id: '2', name: 'Mobile Development' };

      mockRepository.findOne
        .mockResolvedValueOnce(mockService) // First call for findOne(id)
        .mockResolvedValueOnce(existingService); // Second call for name conflict check

      await expect(service.update('1', updateServiceDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('remove', () => {
    it('should remove a service successfully', async () => {
      mockRepository.findOne.mockResolvedValue(mockService);
      mockRepository.remove.mockResolvedValue(mockService);

      await service.remove('1');

      expect(repository.remove).toHaveBeenCalledWith(mockService);
    });

    it('should throw NotFoundException if service not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('toggleActiveStatus', () => {
    it('should toggle service active status', async () => {
      const inactiveService = { ...mockService, isActive: false };
      mockRepository.findOne.mockResolvedValue(mockService);
      mockRepository.save.mockResolvedValue(inactiveService);

      const result = await service.toggleActiveStatus('1');

      expect(result.isActive).toBe(false);
    });
  });

  describe('searchServices', () => {
    it('should search services by name', async () => {
      const services = [mockService];
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockQueryBuilder.getMany.mockResolvedValue(services);

      const result = await service.searchServices('Web');

      expect(repository.createQueryBuilder).toHaveBeenCalledWith('service');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'service.name ILIKE :searchTerm',
        { searchTerm: '%Web%' },
      );
      expect(result).toEqual(services);
    });
  });

  describe('getServiceStats', () => {
    it('should return service statistics', async () => {
      mockRepository.count.mockResolvedValueOnce(10).mockResolvedValueOnce(8);

      const result = await service.getServiceStats();

      expect(result).toEqual({
        total: 10,
        active: 8,
        inactive: 2,
      });
    });
  });
});
