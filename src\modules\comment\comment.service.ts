import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Comment } from './entities/comment.entity';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { User } from '../user/entities/user.entity';

@Injectable()
export class CommentService {
  constructor(
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createCommentDto: CreateCommentDto): Promise<Comment> {
    const comment = this.commentRepository.create(createCommentDto);

    // If authorId is provided, set the author
    if (createCommentDto.authorId) {
      const author = await this.userRepository.findOne({
        where: { id: createCommentDto.authorId },
      });
      if (author) {
        comment.author = author;
      }
    }

    return await this.commentRepository.save(comment);
  }

  async findAll(): Promise<Comment[]> {
    return await this.commentRepository.find({
      relations: ['author', 'parentComment'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllActive(): Promise<Comment[]> {
    return await this.commentRepository.find({
      where: { isActive: true },
      relations: ['author', 'parentComment'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Comment> {
    const comment = await this.commentRepository.findOne({
      where: { id },
      relations: ['author', 'parentComment'],
    });

    if (!comment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }

    return comment;
  }

  async findByEntity(entityType: string, entityId: string): Promise<Comment[]> {
    return await this.commentRepository.find({
      where: {
        isActive: true,
      },
      relations: ['author', 'replies', 'replies.author'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByAuthor(authorId: string): Promise<Comment[]> {
    return await this.commentRepository.find({
      where: { author: { id: authorId } },
      relations: ['author', 'parentComment'],
      order: { createdAt: 'DESC' },
    });
  }

  async findReplies(parentCommentId: string): Promise<Comment[]> {
    return await this.commentRepository.find({
      where: {
        isActive: true,
      },
      relations: ['author'],
      order: { createdAt: 'ASC' },
    });
  }

  async update(
    id: string,
    updateCommentDto: UpdateCommentDto,
  ): Promise<Comment> {
    const comment = await this.findOne(id);

    // Mark as edited if content is being updated
    if (
      updateCommentDto.content &&
      updateCommentDto.content !== comment.content
    ) {
      updateCommentDto.isEdited = true;
    }

    Object.assign(comment, updateCommentDto);
    return await this.commentRepository.save(comment);
  }

  async remove(id: string): Promise<void> {
    const comment = await this.findOne(id);
    await this.commentRepository.remove(comment);
  }

  async softDelete(id: string): Promise<Comment> {
    const comment = await this.findOne(id);
    comment.isActive = false;
    return await this.commentRepository.save(comment);
  }

  async toggleActiveStatus(id: string): Promise<Comment> {
    const comment = await this.findOne(id);
    comment.isActive = !comment.isActive;
    return await this.commentRepository.save(comment);
  }

  async canUserModifyComment(
    commentId: string,
    userId: string,
  ): Promise<boolean> {
    const comment = await this.commentRepository.findOne({
      where: { id: commentId },
      relations: ['author'],
    });

    if (!comment) {
      return false;
    }

    // User can modify their own comments
    return comment.author && comment.author.id === userId;
  }

  async updateByUser(
    id: string,
    updateCommentDto: UpdateCommentDto,
    userId: string,
  ): Promise<Comment> {
    const canModify = await this.canUserModifyComment(id, userId);

    if (!canModify) {
      throw new ForbiddenException('You can only modify your own comments');
    }

    return await this.update(id, updateCommentDto);
  }

  async deleteByUser(id: string, userId: string): Promise<void> {
    const canModify = await this.canUserModifyComment(id, userId);

    if (!canModify) {
      throw new ForbiddenException('You can only delete your own comments');
    }

    await this.softDelete(id);
  }

  async getCommentStats(entityType?: string, entityId?: string) {
    const query = this.commentRepository.createQueryBuilder('comment');

    if (entityType && entityId) {
      query.where(
        'comment.entityType = :entityType AND comment.entityId = :entityId',
        {
          entityType,
          entityId,
        },
      );
    }

    const total = await query.getCount();
    const active = await query
      .andWhere('comment.isActive = :isActive', { isActive: true })
      .getCount();

    return {
      total,
      active,
      inactive: total - active,
    };
  }
}
