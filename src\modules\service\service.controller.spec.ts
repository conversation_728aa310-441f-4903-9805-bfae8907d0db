import { Test, TestingModule } from '@nestjs/testing';
import { ServiceController } from './service.controller';
import { ServiceService } from './service.service';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { Service } from './entities/service.entity';

describe('ServiceController', () => {
  let controller: ServiceController;
  let service: ServiceService;

  const mockService: Service = {
    id: '1',
    name: 'Web Development',
    icon: 'web-icon',
    imageUrl: 'https://example.com/web-dev.jpg',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockServiceService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findAllActive: jest.fn(),
    findOne: jest.fn(),
    findByName: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    toggleActiveStatus: jest.fn(),
    searchServices: jest.fn(),
    getServiceStats: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ServiceController],
      providers: [
        {
          provide: ServiceService,
          useValue: mockServiceService,
        },
      ],
    }).compile();

    controller = module.get<ServiceController>(ServiceController);
    service = module.get<ServiceService>(ServiceService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a service', async () => {
      const createServiceDto: CreateServiceDto = {
        name: 'Web Development',
        icon: 'web-icon',
        imageUrl: 'https://example.com/web-dev.jpg',
        isActive: true,
      };

      mockServiceService.create.mockResolvedValue(mockService);

      const result = await controller.create(createServiceDto);

      expect(service.create).toHaveBeenCalledWith(createServiceDto);
      expect(result).toEqual(mockService);
    });
  });

  describe('findAll', () => {
    it('should return all services', async () => {
      const services = [mockService];
      mockServiceService.findAll.mockResolvedValue(services);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(services);
    });

    it('should return active services when active=true', async () => {
      const services = [mockService];
      mockServiceService.findAllActive.mockResolvedValue(services);

      const result = await controller.findAll(true);

      expect(service.findAllActive).toHaveBeenCalled();
      expect(result).toEqual(services);
    });

    it('should search services when search term provided', async () => {
      const services = [mockService];
      mockServiceService.searchServices.mockResolvedValue(services);

      const result = await controller.findAll(undefined, 'Web');

      expect(service.searchServices).toHaveBeenCalledWith('Web');
      expect(result).toEqual(services);
    });
  });

  describe('findOne', () => {
    it('should return a service by id', async () => {
      mockServiceService.findOne.mockResolvedValue(mockService);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockService);
    });
  });

  describe('findByName', () => {
    it('should return a service by name', async () => {
      mockServiceService.findByName.mockResolvedValue(mockService);

      const result = await controller.findByName('Web Development');

      expect(service.findByName).toHaveBeenCalledWith('Web Development');
      expect(result).toEqual(mockService);
    });
  });

  describe('update', () => {
    it('should update a service', async () => {
      const updateServiceDto: UpdateServiceDto = {
        name: 'Mobile Development',
      };
      const updatedService = { ...mockService, name: 'Mobile Development' };

      mockServiceService.update.mockResolvedValue(updatedService);

      const result = await controller.update('1', updateServiceDto);

      expect(service.update).toHaveBeenCalledWith('1', updateServiceDto);
      expect(result).toEqual(updatedService);
    });
  });

  describe('toggleActiveStatus', () => {
    it('should toggle service active status', async () => {
      const inactiveService = { ...mockService, isActive: false };
      mockServiceService.toggleActiveStatus.mockResolvedValue(inactiveService);

      const result = await controller.toggleActiveStatus('1');

      expect(service.toggleActiveStatus).toHaveBeenCalledWith('1');
      expect(result).toEqual(inactiveService);
    });
  });

  describe('remove', () => {
    it('should remove a service', async () => {
      mockServiceService.remove.mockResolvedValue(undefined);

      await controller.remove('1');

      expect(service.remove).toHaveBeenCalledWith('1');
    });
  });

  describe('getStats', () => {
    it('should return service statistics', async () => {
      const stats = { total: 10, active: 8, inactive: 2 };
      mockServiceService.getServiceStats.mockResolvedValue(stats);

      const result = await controller.getStats();

      expect(service.getServiceStats).toHaveBeenCalled();
      expect(result).toEqual(stats);
    });
  });
});
