import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { ProviderUserServiceService } from './provider-user-service.service';
import { CreateProviderUserServiceDto } from './dto/create-provider-user-service.dto';
import { UpdateProviderUserServiceDto } from './dto/update-provider-user-service.dto';
import { ProviderUserService } from './entities/provider-user-service.entity';
import { Availability } from '../../common/enums/availability.enum';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../common/enums/user-role.enum';
import { RolesGuard } from '../../common/guards/roles.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('provider-user-services')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProviderUserServiceController {
  constructor(private readonly providerUserServiceService: ProviderUserServiceService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async create(@Body() createProviderUserServiceDto: CreateProviderUserServiceDto): Promise<ProviderUserService> {
    return await this.providerUserServiceService.create(createProviderUserServiceDto);
  }

  @Get()
  async findAll(
    @Query('available') available?: boolean,
    @Query('userId') userId?: string,
    @Query('serviceId') serviceId?: string,
    @Query('availability') availability?: Availability,
    @Query('search') search?: string,
    @Query('topRated') topRated?: boolean,
    @Query('limit') limit?: number,
  ): Promise<ProviderUserService[]> {
    if (search) {
      return await this.providerUserServiceService.searchProviders(search);
    }

    if (topRated) {
      return await this.providerUserServiceService.findTopRated(limit || 10);
    }

    if (userId) {
      return await this.providerUserServiceService.findByUser(userId);
    }

    if (serviceId) {
      return await this.providerUserServiceService.findByService(serviceId);
    }

    if (availability) {
      return await this.providerUserServiceService.findByAvailability(availability);
    }

    if (available !== undefined) {
      return available
        ? await this.providerUserServiceService.findAllAvailable()
        : await this.providerUserServiceService.findAll();
    }

    return await this.providerUserServiceService.findAll();
  }

  @Get('stats')
  @Roles(UserRole.ADMIN)
  async getStats(@Query('userId') userId?: string) {
    return await this.providerUserServiceService.getProviderStats(userId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ProviderUserService> {
    return await this.providerUserServiceService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async update(
    @Param('id') id: string,
    @Body() updateProviderUserServiceDto: UpdateProviderUserServiceDto,
  ): Promise<ProviderUserService> {
    return await this.providerUserServiceService.update(id, updateProviderUserServiceDto);
  }

  @Patch(':id/rating')
  async updateRating(
    @Param('id') id: string,
    @Body('rating') rating: number,
  ): Promise<ProviderUserService> {
    return await this.providerUserServiceService.updateRating(id, rating);
  }

  @Patch(':id/toggle-availability')
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async toggleAvailability(@Param('id') id: string): Promise<ProviderUserService> {
    return await this.providerUserServiceService.toggleAvailability(id);
  }

  @Post(':id/contacts/:contactId')
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async addContact(
    @Param('id') id: string,
    @Param('contactId') contactId: string,
  ): Promise<ProviderUserService> {
    return await this.providerUserServiceService.addContact(id, contactId);
  }

  @Delete(':id/contacts/:contactId')
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async removeContact(
    @Param('id') id: string,
    @Param('contactId') contactId: string,
  ): Promise<ProviderUserService> {
    return await this.providerUserServiceService.removeContact(id, contactId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(UserRole.ADMIN, UserRole.PROVIDER)
  async remove(@Param('id') id: string): Promise<void> {
    return await this.providerUserServiceService.remove(id);
  }
}
