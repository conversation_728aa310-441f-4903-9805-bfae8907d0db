import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ProviderUserService } from './entities/provider-user-service.entity';
import { CreateProviderUserServiceDto } from './dto/create-provider-user-service.dto';
import { UpdateProviderUserServiceDto } from './dto/update-provider-user-service.dto';
import { User } from '../user/entities/user.entity';
import { Service } from '../service/entities/service.entity';
import { Contact } from '../contact/entities/contact.entity';
import { UserRole } from '../../common/enums/user-role.enum';
import { Availability } from '../../common/enums/availability.enum';

@Injectable()
export class ProviderUserServiceService {
  constructor(
    @InjectRepository(ProviderUserService)
    private readonly providerUserServiceRepository: Repository<ProviderUserService>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>,
  ) {}

  async create(createProviderUserServiceDto: CreateProviderUserServiceDto): Promise<ProviderUserService> {
    // Validate user exists and is a provider
    const user = await this.userRepository.findOne({
      where: { id: createProviderUserServiceDto.userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${createProviderUserServiceDto.userId} not found`);
    }

    if (user.role !== UserRole.PROVIDER) {
      throw new ForbiddenException('Only users with provider role can offer services');
    }

    // Validate service exists
    const service = await this.serviceRepository.findOne({
      where: { id: createProviderUserServiceDto.serviceId },
    });

    if (!service) {
      throw new NotFoundException(`Service with ID ${createProviderUserServiceDto.serviceId} not found`);
    }

    // Check if provider already offers this service
    const existingProviderService = await this.providerUserServiceRepository.findOne({
      where: {
        user: { id: createProviderUserServiceDto.userId },
        service: { id: createProviderUserServiceDto.serviceId },
      },
    });

    if (existingProviderService) {
      throw new ConflictException('Provider already offers this service');
    }

    // Get contacts if provided
    let contacts: Contact[] = [];
    if (createProviderUserServiceDto.contactIds && createProviderUserServiceDto.contactIds.length > 0) {
      contacts = await this.contactRepository.find({
        where: { id: In(createProviderUserServiceDto.contactIds) },
      });
    }

    const providerUserService = this.providerUserServiceRepository.create({
      user,
      service,
      contacts,
      bio: createProviderUserServiceDto.bio,
      rating: createProviderUserServiceDto.rating || 0,
      reviewCount: createProviderUserServiceDto.reviewCount || 0,
      isAvailable: createProviderUserServiceDto.isAvailable ?? true,
      availability: createProviderUserServiceDto.availability || Availability.ANYTIME,
    });

    return await this.providerUserServiceRepository.save(providerUserService);
  }

  async findAll(): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      relations: ['user', 'service', 'contacts'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllAvailable(): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      where: { isAvailable: true },
      relations: ['user', 'service', 'contacts'],
      order: { rating: 'DESC', reviewCount: 'DESC' },
    });
  }

  async findOne(id: string): Promise<ProviderUserService> {
    const providerUserService = await this.providerUserServiceRepository.findOne({
      where: { id },
      relations: ['user', 'service', 'contacts', 'comments'],
    });

    if (!providerUserService) {
      throw new NotFoundException(`ProviderUserService with ID ${id} not found`);
    }

    return providerUserService;
  }

  async findByUser(userId: string): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      where: { user: { id: userId } },
      relations: ['user', 'service', 'contacts'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByService(serviceId: string): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      where: { service: { id: serviceId } },
      relations: ['user', 'service', 'contacts'],
      order: { rating: 'DESC', reviewCount: 'DESC' },
    });
  }

  async findByAvailability(availability: Availability): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      where: { availability, isAvailable: true },
      relations: ['user', 'service', 'contacts'],
      order: { rating: 'DESC' },
    });
  }

  async findTopRated(limit: number = 10): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository.find({
      where: { isAvailable: true },
      relations: ['user', 'service', 'contacts'],
      order: { rating: 'DESC', reviewCount: 'DESC' },
      take: limit,
    });
  }

  async update(id: string, updateProviderUserServiceDto: UpdateProviderUserServiceDto): Promise<ProviderUserService> {
    const providerUserService = await this.findOne(id);

    // Update contacts if provided
    if (updateProviderUserServiceDto.contactIds) {
      const contacts = await this.contactRepository.find({
        where: { id: In(updateProviderUserServiceDto.contactIds) },
      });
      providerUserService.contacts = contacts;
    }

    // Update other fields
    Object.assign(providerUserService, {
      bio: updateProviderUserServiceDto.bio ?? providerUserService.bio,
      rating: updateProviderUserServiceDto.rating ?? providerUserService.rating,
      reviewCount: updateProviderUserServiceDto.reviewCount ?? providerUserService.reviewCount,
      isAvailable: updateProviderUserServiceDto.isAvailable ?? providerUserService.isAvailable,
      availability: updateProviderUserServiceDto.availability ?? providerUserService.availability,
    });

    return await this.providerUserServiceRepository.save(providerUserService);
  }

  async updateRating(id: string, newRating: number): Promise<ProviderUserService> {
    const providerUserService = await this.findOne(id);
    
    // Calculate new average rating
    const totalRating = (providerUserService.rating * providerUserService.reviewCount) + newRating;
    const newReviewCount = providerUserService.reviewCount + 1;
    const newAverageRating = totalRating / newReviewCount;

    providerUserService.rating = Math.round(newAverageRating * 100) / 100; // Round to 2 decimal places
    providerUserService.reviewCount = newReviewCount;

    return await this.providerUserServiceRepository.save(providerUserService);
  }

  async toggleAvailability(id: string): Promise<ProviderUserService> {
    const providerUserService = await this.findOne(id);
    providerUserService.isAvailable = !providerUserService.isAvailable;
    return await this.providerUserServiceRepository.save(providerUserService);
  }

  async remove(id: string): Promise<void> {
    const providerUserService = await this.findOne(id);
    await this.providerUserServiceRepository.remove(providerUserService);
  }

  async addContact(id: string, contactId: string): Promise<ProviderUserService> {
    const providerUserService = await this.findOne(id);
    const contact = await this.contactRepository.findOne({
      where: { id: contactId },
    });

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${contactId} not found`);
    }

    // Check if contact is already added
    const contactExists = providerUserService.contacts.some(c => c.id === contactId);
    if (!contactExists) {
      providerUserService.contacts.push(contact);
      return await this.providerUserServiceRepository.save(providerUserService);
    }

    return providerUserService;
  }

  async removeContact(id: string, contactId: string): Promise<ProviderUserService> {
    const providerUserService = await this.findOne(id);
    providerUserService.contacts = providerUserService.contacts.filter(c => c.id !== contactId);
    return await this.providerUserServiceRepository.save(providerUserService);
  }

  async getProviderStats(userId?: string): Promise<{
    total: number;
    available: number;
    unavailable: number;
    averageRating: number;
    totalReviews: number;
    byAvailability: Record<Availability, number>;
  }> {
    const whereCondition = userId ? { user: { id: userId } } : {};
    
    const total = await this.providerUserServiceRepository.count({ where: whereCondition });
    const available = await this.providerUserServiceRepository.count({
      where: { ...whereCondition, isAvailable: true },
    });

    const allProviderServices = await this.providerUserServiceRepository.find({
      where: whereCondition,
    });

    const totalRating = allProviderServices.reduce((sum, ps) => sum + ps.rating, 0);
    const totalReviews = allProviderServices.reduce((sum, ps) => sum + ps.reviewCount, 0);
    const averageRating = total > 0 ? totalRating / total : 0;

    const byAvailability: Record<Availability, number> = {} as Record<Availability, number>;
    for (const availability of Object.values(Availability)) {
      byAvailability[availability] = await this.providerUserServiceRepository.count({
        where: { ...whereCondition, availability },
      });
    }

    return {
      total,
      available,
      unavailable: total - available,
      averageRating: Math.round(averageRating * 100) / 100,
      totalReviews,
      byAvailability,
    };
  }

  async searchProviders(searchTerm: string): Promise<ProviderUserService[]> {
    return await this.providerUserServiceRepository
      .createQueryBuilder('providerUserService')
      .leftJoinAndSelect('providerUserService.user', 'user')
      .leftJoinAndSelect('providerUserService.service', 'service')
      .leftJoinAndSelect('providerUserService.contacts', 'contacts')
      .where(
        'user.firstName ILIKE :searchTerm OR user.lastName ILIKE :searchTerm OR service.name ILIKE :searchTerm OR providerUserService.bio ILIKE :searchTerm',
        { searchTerm: `%${searchTerm}%` },
      )
      .orderBy('providerUserService.rating', 'DESC')
      .getMany();
  }
}
