import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProviderUserServiceService } from './provider-user-service.service';
import { ProviderUserService } from './entities/provider-user-service.entity';
import { User } from '../user/entities/user.entity';
import { Service } from '../service/entities/service.entity';
import { Contact } from '../contact/entities/contact.entity';
import { CreateProviderUserServiceDto } from './dto/create-provider-user-service.dto';
import { UpdateProviderUserServiceDto } from './dto/update-provider-user-service.dto';
import { NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { UserRole } from '../../common/enums/user-role.enum';
import { Availability } from '../../common/enums/availability.enum';

describe('ProviderUserServiceService', () => {
  let service: ProviderUserServiceService;
  let providerUserServiceRepository: Repository<ProviderUserService>;
  let userRepository: Repository<User>;
  let serviceRepository: Repository<Service>;
  let contactRepository: Repository<Contact>;

  const mockUser = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: UserRole.PROVIDER,
  };

  const mockService = {
    id: '1',
    name: 'Web Development',
    icon: 'web-icon',
    imageUrl: 'https://example.com/web.jpg',
  };

  const mockContact = {
    id: '1',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
  };

  const mockProviderUserService = {
    id: '1',
    user: mockUser,
    service: mockService,
    contacts: [mockContact],
    bio: 'Experienced web developer',
    rating: 4.5,
    reviewCount: 10,
    isAvailable: true,
    availability: Availability.ANYTIME,
    comments: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockProviderUserServiceRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockServiceRepository = {
    findOne: jest.fn(),
  };

  const mockContactRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProviderUserServiceService,
        {
          provide: getRepositoryToken(ProviderUserService),
          useValue: mockProviderUserServiceRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(Service),
          useValue: mockServiceRepository,
        },
        {
          provide: getRepositoryToken(Contact),
          useValue: mockContactRepository,
        },
      ],
    }).compile();

    service = module.get<ProviderUserServiceService>(ProviderUserServiceService);
    providerUserServiceRepository = module.get<Repository<ProviderUserService>>(
      getRepositoryToken(ProviderUserService),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    serviceRepository = module.get<Repository<Service>>(getRepositoryToken(Service));
    contactRepository = module.get<Repository<Contact>>(getRepositoryToken(Contact));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a provider user service successfully', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
        bio: 'Experienced web developer',
        isAvailable: true,
        availability: Availability.ANYTIME,
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockServiceRepository.findOne.mockResolvedValue(mockService);
      mockProviderUserServiceRepository.findOne.mockResolvedValue(null);
      mockProviderUserServiceRepository.create.mockReturnValue(mockProviderUserService);
      mockProviderUserServiceRepository.save.mockResolvedValue(mockProviderUserService);

      const result = await service.create(createDto);

      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
      expect(serviceRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
      expect(providerUserServiceRepository.create).toHaveBeenCalled();
      expect(providerUserServiceRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockProviderUserService);
    });

    it('should throw NotFoundException if user not found', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
      };

      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not a provider', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
      };

      const nonProviderUser = { ...mockUser, role: UserRole.CLIENT };
      mockUserRepository.findOne.mockResolvedValue(nonProviderUser);

      await expect(service.create(createDto)).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException if service not found', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockServiceRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if provider already offers this service', async () => {
      const createDto: CreateProviderUserServiceDto = {
        userId: '1',
        serviceId: '1',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockServiceRepository.findOne.mockResolvedValue(mockService);
      mockProviderUserServiceRepository.findOne.mockResolvedValue(mockProviderUserService);

      await expect(service.create(createDto)).rejects.toThrow(ConflictException);
    });
  });

  describe('findAll', () => {
    it('should return all provider user services', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceRepository.find.mockResolvedValue(providerServices);

      const result = await service.findAll();

      expect(providerUserServiceRepository.find).toHaveBeenCalledWith({
        relations: ['user', 'service', 'contacts'],
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(providerServices);
    });
  });

  describe('findOne', () => {
    it('should return a provider user service by id', async () => {
      mockProviderUserServiceRepository.findOne.mockResolvedValue(mockProviderUserService);

      const result = await service.findOne('1');

      expect(providerUserServiceRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['user', 'service', 'contacts', 'comments'],
      });
      expect(result).toEqual(mockProviderUserService);
    });

    it('should throw NotFoundException if provider user service not found', async () => {
      mockProviderUserServiceRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByUser', () => {
    it('should return provider services by user', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceRepository.find.mockResolvedValue(providerServices);

      const result = await service.findByUser('1');

      expect(providerUserServiceRepository.find).toHaveBeenCalledWith({
        where: { user: { id: '1' } },
        relations: ['user', 'service', 'contacts'],
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(providerServices);
    });
  });

  describe('findByService', () => {
    it('should return provider services by service', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceRepository.find.mockResolvedValue(providerServices);

      const result = await service.findByService('1');

      expect(providerUserServiceRepository.find).toHaveBeenCalledWith({
        where: { service: { id: '1' } },
        relations: ['user', 'service', 'contacts'],
        order: { rating: 'DESC', reviewCount: 'DESC' },
      });
      expect(result).toEqual(providerServices);
    });
  });

  describe('updateRating', () => {
    it('should update provider rating correctly', async () => {
      const providerService = {
        ...mockProviderUserService,
        rating: 4.0,
        reviewCount: 4,
      };

      mockProviderUserServiceRepository.findOne.mockResolvedValue(providerService);
      
      const updatedProviderService = {
        ...providerService,
        rating: 4.2,
        reviewCount: 5,
      };
      
      mockProviderUserServiceRepository.save.mockResolvedValue(updatedProviderService);

      const result = await service.updateRating('1', 5);

      expect(result.rating).toBe(4.2);
      expect(result.reviewCount).toBe(5);
    });
  });

  describe('searchProviders', () => {
    it('should search providers by term', async () => {
      const providerServices = [mockProviderUserService];
      mockProviderUserServiceRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockQueryBuilder.getMany.mockResolvedValue(providerServices);

      const result = await service.searchProviders('web');

      expect(providerUserServiceRepository.createQueryBuilder).toHaveBeenCalledWith('providerUserService');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(3);
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('providerUserService.rating', 'DESC');
      expect(result).toEqual(providerServices);
    });
  });

  describe('getProviderStats', () => {
    it('should return provider statistics', async () => {
      mockProviderUserServiceRepository.count.mockResolvedValueOnce(10).mockResolvedValueOnce(8);
      mockProviderUserServiceRepository.find.mockResolvedValue([
        { rating: 4.0, reviewCount: 5 },
        { rating: 4.5, reviewCount: 3 },
      ]);

      // Mock availability counts
      for (let i = 0; i < Object.values(Availability).length; i++) {
        mockProviderUserServiceRepository.count.mockResolvedValueOnce(i + 1);
      }

      const result = await service.getProviderStats();

      expect(result.total).toBe(10);
      expect(result.available).toBe(8);
      expect(result.unavailable).toBe(2);
      expect(result.averageRating).toBe(4.25);
      expect(result.totalReviews).toBe(8);
    });
  });
});
