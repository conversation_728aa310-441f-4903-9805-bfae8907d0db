import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ServiceService } from './service.service';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { Service } from './entities/service.entity';

@Controller('services')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  async create(@Body() createServiceDto: CreateServiceDto): Promise<Service> {
    return await this.serviceService.create(createServiceDto);
  }

  @Get()
  async findAll(
    @Query('active') active?: boolean,
    @Query('search') search?: string,
  ): Promise<Service[]> {
    if (search) {
      return await this.serviceService.searchServices(search);
    }

    if (active !== undefined) {
      return active
        ? await this.serviceService.findAllActive()
        : await this.serviceService.findAll();
    }

    return await this.serviceService.findAll();
  }

  @Get('stats')
  async getStats() {
    return await this.serviceService.getServiceStats();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Service> {
    return await this.serviceService.findOne(id);
  }

  @Get('name/:name')
  async findByName(@Param('name') name: string): Promise<Service> {
    return await this.serviceService.findByName(name);
  }

  @Get('category/:categoryId')
  async findByCategory(
    @Param('categoryId') categoryId: string,
  ): Promise<Service[]> {
    return await this.serviceService.findByCategory(categoryId);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateServiceDto: UpdateServiceDto,
  ): Promise<Service> {
    return await this.serviceService.update(id, updateServiceDto);
  }

  @Patch(':id/toggle-status')
  async toggleActiveStatus(@Param('id') id: string): Promise<Service> {
    return await this.serviceService.toggleActiveStatus(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return await this.serviceService.remove(id);
  }
}
