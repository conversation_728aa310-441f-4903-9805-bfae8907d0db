import { BadRequestException, Injectable } from '@nestjs/common';
import * as fs from 'fs';
import { diskStorage } from 'multer';
import * as path from 'path';

@Injectable()
export class UploadService {
  async uploadFile(file: Express.Multer.File): Promise<string> {
    if (!file) {
      throw new BadRequestException('File is missing');
    }
    const validFileTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validFileTypes.includes(file.mimetype)) {
      throw new BadRequestException('Invalid file type');
    }

    return file.filename;
  }

  static getMulterConfig() {
    return {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${path.extname(file.originalname)}`);
        },
      }),
      limits: {
        fileSize: 5 * 1024 * 1024,
      },
    };
  }

  async deleteFile(filename: string): Promise<void> {
    if (!filename) {
      return;
    }

    // Check if the filename is a URL
    if (filename.startsWith('http://') || filename.startsWith('https://')) {
      return;
    }

    // Use a relative path from the project root
    const uploadsDirectory = path.join(process.cwd(), 'uploads');
    const filePath = path.join(uploadsDirectory, path.basename(filename));

    if (fs.existsSync(filePath)) {
      await fs.promises.unlink(filePath);
    } else {
      return;
    }
  }
}
