import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  <PERSON>inC<PERSON>umn,
} from 'typeorm';
import { Order } from '../../order/entities/order.entity';
import { NotificationType } from '../entities/enum/notification-type.enum'; // Importing the NotificationType enum

@Entity('notification')
export class Notification {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({ type: 'timestamp' })
  sentAt: Date;

  @ManyToOne(() => Order, (order) => order.notifications, { nullable: true })
  @JoinColumn({ name: 'order_id' })
  order: Order;
}
