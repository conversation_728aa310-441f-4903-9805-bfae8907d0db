import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { NotificationType } from '../entities/enum/notification-type.enum';

@Entity('notification')
export class Notification {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({ type: 'timestamp' })
  sentAt: Date;
}
