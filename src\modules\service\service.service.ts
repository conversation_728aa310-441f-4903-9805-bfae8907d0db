import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Service } from './entities/service.entity';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { Category } from '../category/entities/category.entity';

@Injectable()
export class ServiceService {
  constructor(
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  async create(createServiceDto: CreateServiceDto): Promise<Service> {
    // Check if service with same name already exists
    const existingService = await this.serviceRepository.findOne({
      where: { name: createServiceDto.name },
    });

    if (existingService) {
      throw new ConflictException(
        `Service with name ${createServiceDto.name} already exists`,
      );
    }

    // If categoryId is provided, verify the category exists
    let category = null;
    if (createServiceDto.categoryId) {
      category = await this.categoryRepository.findOne({
        where: { id: createServiceDto.categoryId },
      });

      if (!category) {
        throw new NotFoundException(
          `Category with ID ${createServiceDto.categoryId} not found`,
        );
      }
    }

    const service = this.serviceRepository.create({
      ...createServiceDto,
      category,
    });
    return await this.serviceRepository.save(service);
  }

  async findAll(): Promise<Service[]> {
    return await this.serviceRepository.find({
      relations: ['category'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllActive(): Promise<Service[]> {
    return await this.serviceRepository.find({
      where: { isActive: true },
      relations: ['category'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Service> {
    const service = await this.serviceRepository.findOne({
      where: { id },
      relations: ['category'],
    });

    if (!service) {
      throw new NotFoundException(`Service with ID ${id} not found`);
    }

    return service;
  }

  async findByName(name: string): Promise<Service> {
    const service = await this.serviceRepository.findOne({
      where: { name },
      relations: ['category'],
    });

    if (!service) {
      throw new NotFoundException(`Service with name ${name} not found`);
    }

    return service;
  }

  async findByCategory(categoryId: string): Promise<Service[]> {
    return await this.serviceRepository.find({
      where: { category: { id: categoryId } },
      relations: ['category'],
      order: { createdAt: 'DESC' },
    });
  }

  async update(
    id: string,
    updateServiceDto: UpdateServiceDto,
  ): Promise<Service> {
    const service = await this.findOne(id);

    // If name is being updated, check for conflicts
    if (updateServiceDto.name && updateServiceDto.name !== service.name) {
      const existingService = await this.serviceRepository.findOne({
        where: { name: updateServiceDto.name },
      });

      if (existingService) {
        throw new ConflictException(
          `Service with name ${updateServiceDto.name} already exists`,
        );
      }
    }

    // If categoryId is being updated, verify the category exists
    if (updateServiceDto.categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: updateServiceDto.categoryId },
      });

      if (!category) {
        throw new NotFoundException(
          `Category with ID ${updateServiceDto.categoryId} not found`,
        );
      }

      service.category = category;
    }

    Object.assign(service, updateServiceDto);
    return await this.serviceRepository.save(service);
  }

  async remove(id: string): Promise<void> {
    const service = await this.findOne(id);
    await this.serviceRepository.remove(service);
  }

  async toggleActiveStatus(id: string): Promise<Service> {
    const service = await this.findOne(id);
    service.isActive = !service.isActive;
    return await this.serviceRepository.save(service);
  }

  async searchServices(searchTerm: string): Promise<Service[]> {
    return await this.serviceRepository
      .createQueryBuilder('service')
      .where('service.name ILIKE :searchTerm', {
        searchTerm: `%${searchTerm}%`,
      })
      .orderBy('service.createdAt', 'DESC')
      .getMany();
  }

  async getServiceStats() {
    const total = await this.serviceRepository.count();
    const active = await this.serviceRepository.count({
      where: { isActive: true },
    });

    return {
      total,
      active,
      inactive: total - active,
    };
  }
}
