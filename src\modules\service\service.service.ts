import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Service } from './entities/service.entity';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';

@Injectable()
export class ServiceService {
  constructor(
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
  ) {}

  async create(createServiceDto: CreateServiceDto): Promise<Service> {
    // Check if service with same name already exists
    const existingService = await this.serviceRepository.findOne({
      where: { name: createServiceDto.name },
    });

    if (existingService) {
      throw new ConflictException(
        `Service with name ${createServiceDto.name} already exists`,
      );
    }

    const service = this.serviceRepository.create(createServiceDto);
    return await this.serviceRepository.save(service);
  }

  async findAll(): Promise<Service[]> {
    return await this.serviceRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findAllActive(): Promise<Service[]> {
    return await this.serviceRepository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Service> {
    const service = await this.serviceRepository.findOne({
      where: { id },
    });

    if (!service) {
      throw new NotFoundException(`Service with ID ${id} not found`);
    }

    return service;
  }

  async findByName(name: string): Promise<Service> {
    const service = await this.serviceRepository.findOne({
      where: { name },
    });

    if (!service) {
      throw new NotFoundException(`Service with name ${name} not found`);
    }

    return service;
  }

  async update(id: string, updateServiceDto: UpdateServiceDto): Promise<Service> {
    const service = await this.findOne(id);

    // If name is being updated, check for conflicts
    if (updateServiceDto.name && updateServiceDto.name !== service.name) {
      const existingService = await this.serviceRepository.findOne({
        where: { name: updateServiceDto.name },
      });

      if (existingService) {
        throw new ConflictException(
          `Service with name ${updateServiceDto.name} already exists`,
        );
      }
    }

    Object.assign(service, updateServiceDto);
    return await this.serviceRepository.save(service);
  }

  async remove(id: string): Promise<void> {
    const service = await this.findOne(id);
    await this.serviceRepository.remove(service);
  }

  async toggleActiveStatus(id: string): Promise<Service> {
    const service = await this.findOne(id);
    service.isActive = !service.isActive;
    return await this.serviceRepository.save(service);
  }

  async searchServices(searchTerm: string): Promise<Service[]> {
    return await this.serviceRepository
      .createQueryBuilder('service')
      .where('service.name ILIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('service.createdAt', 'DESC')
      .getMany();
  }

  async getServiceStats() {
    const total = await this.serviceRepository.count();
    const active = await this.serviceRepository.count({
      where: { isActive: true },
    });

    return {
      total,
      active,
      inactive: total - active,
    };
  }
}
