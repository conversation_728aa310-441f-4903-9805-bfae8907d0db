import { IsString, IsOptional, IsBoolean, IsEnum, IsNumber, IsArray, Min, Max } from 'class-validator';
import { Availability } from '../../../common/enums/availability.enum';

export class CreateProviderUserServiceDto {
  @IsString()
  userId: string;

  @IsString()
  serviceId: string;

  @IsArray()
  @IsOptional()
  contactIds?: string[];

  @IsString()
  @IsOptional()
  bio?: string;

  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  rating?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  reviewCount?: number;

  @IsBoolean()
  @IsOptional()
  isAvailable?: boolean;

  @IsEnum(Availability)
  @IsOptional()
  availability?: Availability;
}
