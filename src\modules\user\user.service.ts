import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserRole } from '../../common/enums/user-role.enum';
import { UserRoleService } from './services/user-role.service';
import { UploadService } from '../shared/upload.service';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly userRoleService: UserRoleService,
    private readonly uploadService: UploadService,
  ) {}

  /**
   * Create a new user
   */
  async create(createUserData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone?: string;
    city?: string;
    address?: string;
    role?: UserRole;
    imageUrl?: string;
    activated?: boolean;
  }): Promise<User> {
    // Check if user with same email already exists
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserData.email },
    });

    if (existingUser) {
      throw new ConflictException(
        `User with email ${createUserData.email} already exists`,
      );
    }

    // Check if phone is provided and already exists
    if (createUserData.phone) {
      const existingUserByPhone = await this.userRepository.findOne({
        where: { phone: createUserData.phone },
      });

      if (existingUserByPhone) {
        throw new ConflictException(
          `User with phone ${createUserData.phone} already exists`,
        );
      }
    }

    const user = this.userRepository.create({
      ...createUserData,
      role: createUserData.role || UserRole.CLIENT,
    });

    return await this.userRepository.save(user);
  }

  /**
   * Create user from DTO (for registration)
   */
  async createFromDto(createUserDto: CreateUserDto): Promise<User> {
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    return await this.create({
      ...createUserDto,
      password: hashedPassword,
    });
  }

  /**
   * Find all users
   */
  async findAll(): Promise<User[]> {
    return await this.userRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find users by role
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return await this.userRepository.find({
      where: { role },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find one user by ID
   */
  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  /**
   * Find user by email (for authentication)
   */
  async findByEmail(email: string): Promise<User | null> {
    return await this.userRepository
      .createQueryBuilder('user')
      .addSelect('user.password')
      .where('user.email = :email', { email })
      .getOne();
  }

  /**
   * Get user with relations (for auth)
   */
  async getUserWithRelations(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        city: true,
        address: true,
        imageUrl: true,
        role: true,
        activated: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  /**
   * Update user
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // Check email uniqueness if email is being updated
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException(
          `User with email ${updateUserDto.email} already exists`,
        );
      }
    }

    // Check phone uniqueness if phone is being updated
    if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
      const existingUser = await this.userRepository.findOne({
        where: { phone: updateUserDto.phone },
      });

      if (existingUser) {
        throw new ConflictException(
          `User with phone ${updateUserDto.phone} already exists`,
        );
      }
    }

    // Handle image upload cleanup
    if (
      updateUserDto.imageUrl &&
      user.imageUrl &&
      updateUserDto.imageUrl !== user.imageUrl
    ) {
      try {
        await this.uploadService.deleteFile(user.imageUrl);
      } catch (error) {
        // Log error but don't fail the update
        console.warn('Failed to delete old image:', error);
      }
    }

    Object.assign(user, updateUserDto);
    return await this.userRepository.save(user);
  }

  /**
   * Update user role (admin only)
   */
  async updateUserRole(
    id: string,
    role: UserRole,
    currentUser: User,
  ): Promise<User> {
    // Check if current user can update roles
    if (!this.userRoleService.canManageUsers(currentUser)) {
      throw new ForbiddenException(
        'You do not have permission to update user roles',
      );
    }

    const user = await this.findOne(id);

    // Validate role assignment
    this.userRoleService.validateRoleAssignment(currentUser, role);

    // Check if user can update this specific user's role
    if (!this.userRoleService.canUpdateUserRole(currentUser, user)) {
      throw new ForbiddenException("You cannot update this user's role");
    }

    user.role = role;
    return await this.userRepository.save(user);
  }

  /**
   * Remove user
   */
  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);

    // Clean up user's image if exists
    if (user.imageUrl) {
      try {
        await this.uploadService.deleteFile(user.imageUrl);
      } catch (error) {
        console.warn('Failed to delete user image:', error);
      }
    }

    await this.userRepository.remove(user);
  }

  /**
   * Toggle user activation status
   */
  async toggleActivation(id: string): Promise<User> {
    const user = await this.findOne(id);
    user.activated = !user.activated;
    return await this.userRepository.save(user);
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRole, number>;
  }> {
    const total = await this.userRepository.count();
    const active = await this.userRepository.count({
      where: { activated: true },
    });

    const byRole: Record<UserRole, number> = {} as Record<UserRole, number>;

    for (const role of Object.values(UserRole)) {
      byRole[role] = await this.userRepository.count({
        where: { role },
      });
    }

    return {
      total,
      active,
      inactive: total - active,
      byRole,
    };
  }

  /**
   * Search users
   */
  async searchUsers(searchTerm: string): Promise<User[]> {
    return await this.userRepository
      .createQueryBuilder('user')
      .where(
        'user.firstName ILIKE :searchTerm OR user.lastName ILIKE :searchTerm OR user.email ILIKE :searchTerm',
        { searchTerm: `%${searchTerm}%` },
      )
      .orderBy('user.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId: string): Promise<{
    canAccessAdmin: boolean;
    canManageServices: boolean;
    canBookServices: boolean;
    canManageUsers: boolean;
    canViewReports: boolean;
  }> {
    const user = await this.findOne(userId);
    return this.userRoleService.getUserPermissions(user);
  }

  /**
   * Get user dashboard configuration
   */
  async getUserDashboardConfig(userId: string): Promise<{
    dashboardType: string;
    allowedMenuItems: string[];
    defaultRoute: string;
  }> {
    const user = await this.findOne(userId);
    return this.userRoleService.getRoleDashboardConfig(user);
  }

  /**
   * Legacy method for backward compatibility with auth service
   */
  async assignRole(userId: string, role: any): Promise<void> {
    // This method is kept for backward compatibility
    // In the new system, roles are assigned directly in the user entity
    console.warn(
      'assignRole method is deprecated. Use updateUserRole instead.',
    );
  }
}
